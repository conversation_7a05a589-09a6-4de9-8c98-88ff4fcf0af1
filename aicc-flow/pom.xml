<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>aicc-platform</artifactId>
        <groupId>com.iflytek.aicc</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>aicc-flow</artifactId>

    <properties>
        <ql-express.version>3.3.1</ql-express.version>
        <snappy.java.version>1.1.10.7</snappy.java.version>

        <protobuf.version>3.14.0</protobuf.version>
        <protobuf-plugin.version>0.6.1</protobuf-plugin.version>
        <grpc.version>1.35.0</grpc.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.aicc</groupId>
            <artifactId>aicc-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.outbound.sdk</groupId>
            <artifactId>upload-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek</groupId>
            <artifactId>seclib</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.aicc.sdk</groupId>
            <artifactId>aicc-task-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.medicalboot</groupId>
            <artifactId>starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.logtracer</groupId>
            <artifactId>log-tracer-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>QLExpress</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.outbound.sdk</groupId>
            <artifactId>outbound-common-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>QLExpress</artifactId>
            <version>${ql-express.version}</version>
        </dependency>

        <dependency>
            <groupId>org.xerial.snappy</groupId>
            <artifactId>snappy-java</artifactId>
            <version>${snappy.java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.outbound.sdk</groupId>
            <artifactId>uap-center-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-stub</artifactId>
            <version>${grpc.version}</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
            <version>${grpc.version}</version>
        </dependency>
        <dependency>
            <!-- Java 9+ compatibility - Do NOT update to 2.0.0 -->
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>1.3.5</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>net.devh</groupId>
            <artifactId>grpc-spring-boot-starter</artifactId>
            <version>2.11.0.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.medicalboot</groupId>
            <artifactId>logging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.4</version>
        </dependency>
        <dependency>
            <groupId>org.yeauty</groupId>
            <artifactId>netty-websocket-spring-boot-starter</artifactId>
        </dependency>
<!--        spring-boot-starter-websocket-->
    </dependencies>
    <profiles>
        <profile>
            <id>tomcat</id>
            <properties>
                <profiles.active>tomcat</profiles.active>
                <maven.test.skip>true</maven.test.skip>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.iflytek.medicalboot</groupId>
                    <artifactId>starter</artifactId>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>tongWeb</id>
            <properties>
                <profiles.active>tongWeb</profiles.active>
                <maven.test.skip>true</maven.test.skip>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.iflytek.medicalboot</groupId>
                    <artifactId>starter</artifactId>
                    <exclusions>
                        <exclusion>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-starter-tomcat</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
                <dependency>
                    <groupId>com.tongweb.springboot</groupId>
                    <artifactId>tongweb-spring-boot-starter-2.x</artifactId>
                    <version>7.0.E.6</version>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>
    <build>
        <finalName>${project.artifactId}</finalName>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.7.0</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>checkstyle-validation</id>
                        <phase>validate</phase>
                        <configuration>
                            <includeTestSourceDirectory>true</includeTestSourceDirectory>
                            <violationSeverity>error</violationSeverity>
                            <consoleOutput>true</consoleOutput>
                            <failOnViolation>true</failOnViolation>
                            <failsOnError>true</failsOnError>
                        </configuration>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>8.14</version>
                    </dependency>
                </dependencies>
            </plugin>
            <!-- protobuf -->
            <plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
                <version>${protobuf-plugin.version}</version>
                <configuration>
                    <protocArtifact>com.google.protobuf:protoc:${protobuf.version}:exe:${os.detected.classifier}</protocArtifact>
                    <pluginId>grpc-java</pluginId>
                    <!--设置grpc生成代码到指定路径-->
<!--                    <outputDirectory>${project.basedir}/src/main/java/com/iflytek/aicc/flow/grpc/</outputDirectory>-->
                    <!--生成代码前是否清空目录-->
<!--                    <clearOutputDirectory>false</clearOutputDirectory>-->
                    <!--                    <protoSourceRoot>src/main/java/proto</protoSourceRoot>-->
                    <pluginArtifact>io.grpc:protoc-gen-grpc-java:${grpc.version}:exe:${os.detected.classifier}</pluginArtifact>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compile-custom</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-enforcer-plugin</artifactId>-->
<!--                <version>1.4.1</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>enforce</id>-->
<!--                        <goals>-->
<!--                            <goal>enforce</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <rules>-->
<!--                                <requireUpperBoundDeps />-->
<!--                            </rules>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
            <!-- 设置多个源文件夹 -->
<!--            <plugin>-->
<!--                <groupId>org.codehaus.mojo</groupId>-->
<!--                <artifactId>build-helper-maven-plugin</artifactId>-->
<!--                <version>3.0.0</version>-->
<!--                <executions>-->
<!--                    &lt;!&ndash; 添加主源码目录 &ndash;&gt;-->
<!--                    <execution>-->
<!--                        <id>add-source</id>-->
<!--                        <phase>generate-sources</phase>-->
<!--                        <goals>-->
<!--                            <goal>add-source</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <sources>-->
<!--                                <source>${project.basedir}/src/main/gen</source>-->
<!--                                <source>${project.basedir}/src/main/java</source>-->
<!--                            </sources>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>