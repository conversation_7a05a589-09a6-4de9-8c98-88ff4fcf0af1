<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechSceneConfigMapper">
    <resultMap type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechSceneConfigDO"
               id="SpeechSceneConfigMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="value" column="value" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="operationId" column="operation_id" jdbcType="BIGINT"/>
        <result property="operationName" column="operation_name" jdbcType="VARCHAR"/>
        <result property="operationValue" column="operation_value" jdbcType="VARCHAR"/>
        <result property="operationType" column="operation_type" jdbcType="INTEGER"/>
        <result property="operationParentId" column="operation_parent_id" jdbcType="BIGINT"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>


    <sql id="Column_List">
        id,type,name,value,parent_id,remark,create_time,creator,update_time,
        updater,operation_id,operation_name,operation_value,operation_type,operation_parent_id,deleted
    </sql>

    <select id="getSpeechConfigList"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechSceneConfigDO">
        select
        <include refid="Column_List"/>
        from tb_aicc_speech_scene_config where PARENT_ID = #{parentId} AND DELETED = 0

    </select>
    <select id="checkNameRepeat" resultType="java.lang.Integer">
        select count(1) from tb_aicc_speech_scene_config
        where (NAME = #{name} or VALUE = #{value}) and PARENT_ID = #{parentId} AND DELETED = 0
        <if test="id != null">
            AND ID != #{id}
        </if>
    </select>
    <select id="getSpeechConfigListByType"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechSceneConfigDO">
        select
        <include refid="Column_List"/>
        from tb_aicc_speech_scene_config where type = #{type} AND DELETED = 0
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id">
        insert into tb_aicc_speech_scene_config(id,type,name,value,parent_id,remark,
        create_time,creator,update_time,updater,operation_id,operation_name,operation_value,operation_type,
        operation_parent_id,deleted)
        values (#{id},#{type},#{name},#{value},#{parentId},#{remark},#{createTime},
        #{creator},#{updateTime},#{updater},#{operationId},#{operationName},
        #{operationValue},#{operationType}, #{operationParentId},#{deleted})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id">
        insert into tb_aicc_speech_scene_config(id,type,name,value,parent_id,
        remark,create_time,creator,update_time,updater,operation_id,operation_name,
        operation_value, operation_type,operation_parent_id,deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.id},#{entity.type},#{entity.name},#{entity.value},#{entity.parentId},
            #{entity.remark},#{entity.createTime},#{entity.creator},#{entity.updateTime},
            #{entity.updater},#{entity.operationId},#{entity.operationName},
            #{entity.operationValue},#{entity.operationType}, #{entity.operationParentId},
            #{entity.deleted}
            )
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="batchInsert" keyProperty="id">
        insert into tb_aicc_speech_scene_config(id,type,name,value,parent_id,remark,
        create_time,creator,update_time,updater,operation_id,operation_name,
        operation_value,operation_type, operation_parent_id,deleted)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id},#{entity.type},#{entity.name},#{entity.value},#{entity.parentId},
            #{entity.remark},#{entity.createTime},#{entity.creator},#{entity.updateTime},
            #{entity.updater},#{entity.operationId},#{entity.operationName},
            #{entity.operationValue},#{entity.operationType}, #{entity.operationParentId},
            #{entity.deleted})
        </foreach>
    </insert>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from tb_aicc_speech_scene_config where id = #{id}
    </delete>
</mapper>