<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.PlatCodeUserMapper">
    <resultMap type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.PlatCodeUserDO" id="PlatCodeUserMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="platCode" column="plat_code" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="deletedTime" column="deleted_time" jdbcType="DATE"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="PlatCodeUserMap">
        select
        id,plat_code,user_id,create_time,creator,deleted_time,update_time,updater,deleted
        from tb_aicc_plat_code_user
        where id = #{id}
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from tb_aicc_plat_code_user
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="platCode != null and platCode != ''">
                and plat_code = #{platCode}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="deletedTime != null and deletedTime != ''">
                and deleted_time = #{deletedTime}
            </if>
            <if test="deleted != null and deleted != ''">
                and deleted = #{deleted}
            </if>
        </where>
    </select>

    <select id="platCodeUserList"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.PlatCodeUserDO">
        select
        id,plat_code,user_id,create_time,creator,deleted_time,update_time,updater,deleted
        from tb_aicc_plat_code_user
        where deleted = 0
        <if test="userIds != null and userIds.size() > 0 ">
            and user_id in
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="platCodes != null and platCodes.size() > 0">
            <foreach item="item" index="index" collection="platCodes" open=" AND (" separator=" OR " close=")">
                plat_code LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>


    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id">
        insert into tb_aicc_plat_code_user(
        id,plat_code,user_id,create_time,creator,deleted_time,update_time,updater,deleted
        )
        values (
        #{id},#{platCode},#{userId},#{createTime},#{creator},#{deletedTime},#{updateTime},#{updater},#{deleted}
        )
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id">
        insert into tb_aicc_plat_code_user(id,plat_code,user_id,create_time,creator,deleted_time,
        update_time,updater,deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.platCode},#{entity.userId},#{entity.createTime},
            #{entity.creator},#{entity.deletedTime},
            #{entity.updateTime},#{entity.updater},#{entity.deleted})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tb_aicc_plat_code_user(id,plat_code,user_id,create_time,creator,
        deleted_time,update_time,updater,deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.platCode},#{entity.userId},#{entity.createTime},
            #{entity.creator},#{entity.deletedTime},
            #{entity.updateTime},#{entity.updater},#{entity.deleted})
        </foreach>
        on duplicate key update
        id=values(id),
        plat_code=values(plat_code),
        user_id=values(user_id),
        create_time=values(create_time),
        creator=values(creator),
        deleted_time=values(deleted_time),
        update_time=values(updateTime),
        updater=values(updater),
        deleted=values(deleted)
    </insert>


    <update id="logicDelete">
        update tb_aicc_plat_code_user
        <set>
            <if test="deletedTime != null">
                deleted_time = #{deletedTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="updater != null">
                updater = #{updater}
            </if>
        </set>
        where user_id = #{userId}
        and deleted = 0
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from tb_aicc_plat_code_user where id = #{id}
    </delete>
</mapper>