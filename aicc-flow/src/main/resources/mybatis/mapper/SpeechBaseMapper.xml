<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechBaseMapper">

    <sql id="Column_Select">
        id
        ,md5_value
        ,speech_name
        ,speech_intro
        ,flow_file_path
        ,publish_file_path
        ,pdf_url
        ,speech_label
        ,variables
        ,resource_id
        ,status
        ,lock_account
        ,lock_name
        ,deleted
        ,created_by
        ,created_time
        ,updated_by
        ,updated_time
        ,speech_type
        ,publish_time
        ,publish_by
        ,temp_flow_file_path
        ,business_direction
        ,speech_scene
        ,plat_code
        ,delivery_llm_switch
    </sql>

    <sql id="Column_List">
        ta.tenant_id
        ,ta.revision
        ,ta.created_by
        ,ta.created_time
        ,ta.updated_by
        ,ta.updated_time
        ,ta.id
        ,ta.md5_value
        ,ta.speech_name
        ,ta.speech_intro
        ,ta.flow_file_path
        ,ta.publish_file_path
        ,ta.pdf_url
        ,ta.speech_label
        ,ta.variables
        ,ta.resource_id
        ,ta.status
        ,ta.lock_account
        ,ta.lock_name
        ,ta.deleted
        ,ta.speech_type
        ,ta.publish_time
        ,ta.publish_by
        ,ta.temp_flow_file_path
        ,ta.speech_mode
        ,ta.model_stage
        ,ta.speech_scene
        ,ta.business_direction
        ,ta.plat_code
        ,ta.range
        ,ta.delivery_llm_switch
        ,tb.speech_fragment_id
    </sql>
    <sql id="queryList">
        tenant_id
        ,revision
        ,created_by
        ,created_time
        ,updated_by
        ,updated_time
        ,id
        ,md5_value
        ,speech_name
        ,speech_intro
        ,flow_file_path
        ,publish_file_path
        ,pdf_url
        ,speech_label
        ,variables
        ,resource_id
        ,status
        ,lock_account
        ,lock_name
        ,deleted
        ,speech_type
        ,publish_time
        ,publish_by
        ,temp_flow_file_path
        ,speech_mode
        ,model_stage
        ,speech_scene
        ,business_direction
        ,plat_code
        ,range
        ,delivery_llm_switch
    </sql>
    <update id="updateSpeechById">
        UPDATE tb_aicc_speech_base
        <set>
            updated_time = #{updatedTime},
            updated_by = #{updatedBy},
            lock_account = #{lockAccount},
            temp_flow_file_path = #{tempFlowFilePath},
            model_stage = #{modelStage},
            <if test="variables != null">
                variables = #{variables},
            </if>
            <if test="speechName != null">
                speech_name = #{speechName},
            </if>
            <if test="speechIntro != null">
                speech_intro = #{speechIntro},
            </if>
            <if test="flowFilePath != null">
                flow_file_path = #{flowFilePath},
            </if>
            <if test="publishFilePath != null">
                publish_file_path = #{publishFilePath},
            </if>
            <if test="md5Value != null">
                md5_value = #{md5Value},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="speechFragmentId != null">
                speech_fragment_id = #{speechFragmentId},
            </if>
            <if test="publishBy != null">
                publish_by = #{publishBy},
            </if>
            <if test="publishTime != null">
                publish_time = #{publishTime},
            </if>
            <if test="speechMode != null">
                speech_mode = #{speechMode},
            </if>
            <if test="speechScene != null">
                speech_scene = #{speechScene},
            </if>
            <if test="businessDirection != null">
                business_direction = #{businessDirection},
            </if>
            <if test="platCode != null ">
                plat_code = #{platCode},
            </if>
            <if test="range != null">
                range = #{range},
            </if>
            <if test="deliveryLlmSwitch != null ">
                delivery_llm_switch = #{deliveryLlmSwitch}
            </if>
        </set>
        where id = #{id}
    </update>
    <delete id="deleteSpeechById">
        update tb_aicc_speech_base set deleted = 1, updated_by = #{updater}, updated_time = #{updatedTime} where id =
        #{id} and deleted = 0
    </delete>

    <select id="getSpeechBySpeechName" resultType="com.iflytek.aicc.flow.domain.manage.entity.SpeechBaseEntity">
        select
        <include refid="Column_Select"/>
        from
        tb_aicc_speech_base
        where
        deleted = 0 and speech_name = #{speechName}
        <if test="id != null">
            AND id != #{id}
        </if>
        <if test="speechType != null ">
            AND speech_type = #{speechType}
        </if>
        limit 1
    </select>

    <select id="getManagerById" resultType="com.iflytek.aicc.flow.domain.manage.entity.SpeechBaseEntity">
        select
        <include refid="Column_Select"/>
        from tb_aicc_speech_base where id = #{id} and deleted = 0
    </select>

    <select id="listSpeech" resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechBaseDO">
        select tc.*
        from (
        select
        <include refid="Column_List"/>
        from tb_aicc_speech_base ta
        LEFT JOIN (
        SELECT
        speech_id,
        array_to_string(ARRAY_AGG(speech_fragment_id::text), ',') AS speech_fragment_id
        FROM tb_aicc_speech_base_fragment_rel
        where deleted = 0
        GROUP BY speech_id
        ) tb
        ON ta.id = tb.speech_id
        ) tc
        where tc.deleted = 0
        <if test="ids != null and ids.size != 0">
            and tc.id in
            <foreach collection="ids" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="speechName != null and speechName != ''">
            <choose>
                <when test="fullMatchFlag == true">
                    and tc.speech_name = #{speechName}
                </when>
                <otherwise>
                    and tc.speech_name like concat('%',#{speechName},'%')
                </otherwise>
            </choose>
        </if>
        <if test="creator != null and creator != '' ">
            and tc.created_by = #{creator}
        </if>
        <if test="speechType != null">
            and tc.speech_type = #{speechType}
        </if>
        <if test="status != null">
            and tc.status = #{status}
        </if>
        <if test="speechMode != null">
            and tc.speech_mode = #{speechMode}
        </if>
        <if test="modelStage != null">
            and tc.model_stage = #{modelStage}
        </if>
        <if test="speechFragmentIds != null and speechFragmentIds.size() > 0">
            <foreach item="item" index="index" collection="speechFragmentIds" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    tc.speech_fragment_id like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
        <if test="businessDirection != null and businessDirection != '' ">
            and tc.business_direction = #{businessDirection}
        </if>
        <if test="speechScene != null and speechScene.size() > 0">
            <foreach item="item" index="index" collection="speechScene" open=" AND (" separator=" OR " close=")">
                tc.speech_scene LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <if test="range != null and range.size() > 0">
            <foreach item="item" index="index" collection="range" open=" AND (" separator=" OR " close=")">
                tc.range LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <choose>
            <!--交付-->
            <when test="deliveryRole">
                <!--标准话术资源（所有运营角色创建的话术）-->
                <if test="viewType == 1 and userIds.size() > 0 ">
                    and tc.created_by not in
                    <foreach collection="userIds" item="userId" index="index" separator="," open="(" close=")">
                        #{userId}
                    </foreach>
                </if>
                <!--其他机构资源-->
                <if test="viewType == 3">
                    <if test="userIds != null and  userIds.size() > 0 ">
                        and tc.created_by in
                        <foreach collection="userIds" item="userId" index="index" separator="," open="(" close=")">
                            #{userId}
                        </foreach>
                    </if>
                    <if test="selfPlatCodes != null and selfPlatCodes.size() > 0 ">
                        <foreach item="item" index="index" collection="selfPlatCodes" open=" AND (" separator=" OR " close=")">
                            tc.plat_code not LIKE CONCAT('%', #{item}, '%')
                        </foreach>
                    </if>

                    <if test="platCodes != null and platCodes.size() > 0  ">
                        <foreach item="item" index="index" collection="platCodes" open=" AND (" separator=" OR " close=")">
                            tc.plat_code LIKE CONCAT('%', #{item}, '%')
                        </foreach>
                    </if>
                </if>
                <!--我的机构资源-->
                <if test="viewType == 4">

                    <if test="userIds != null  and  userIds.size() > 0">
                        and tc.created_by in
                        <foreach collection="userIds" item="userId" index="index" separator="," open="(" close=")">
                            #{userId}
                        </foreach>
                    </if>
                    <if test="selfPlatCodes != null and selfPlatCodes.size() > 0  ">
                        <foreach item="item" index="index" collection="selfPlatCodes" open=" AND (" separator=" OR " close=")">
                            tc.plat_code LIKE CONCAT('%', #{item}, '%')
                        </foreach>
                    </if>
                </if>
            </when>
            <!--运营-->
            <otherwise>
                <!--标准话术资源（所有运营角色创建的话术）-->
                <if test="viewType == 1 and userIds.size() > 0 ">
                    and tc.created_by not in
                    <foreach collection="userIds" item="userId" index="index" separator="," open="(" close=")">
                        #{userId}
                    </foreach>
                </if>
                <!--交付话术资源（所有交付角色创建的话术）-->
                <if test="viewType == 2 and userIds.size() > 0 ">
                    and tc.created_by in
                    <foreach collection="userIds" item="userId" index="index" separator="," open="(" close=")">
                        #{userId}
                    </foreach>
                </if>

            </otherwise>
        </choose>
        order by tc.created_time desc
    </select>
    <select id="queryList"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechBaseDO">
        SELECT
        <include refid="queryList"/>
        FROM
        tb_aicc_speech_base
        WHERE deleted = 0
        <if test="ids != null and ids.size != 0">
            and id in
            <foreach collection="ids" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="speechName != null and speechName != ''">
            <choose>
                <when test="fullMatchFlag == true">
                    and speech_name = #{speechName}
                </when>
                <otherwise>
                    and speech_name like concat('%',#{speechName},'%')
                </otherwise>
            </choose>
        </if>
        <if test="creator != null and creator != '' ">
            and created_by = #{creator}
        </if>
        <if test="speechType != null">
            and speech_type = #{speechType}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="speechMode != null">
            and speech_mode = #{speechMode}
        </if>
        <if test="modelStage != null">
            and model_stage = #{modelStage}
        </if>
        <if test="speechFragmentIds != null and speechFragmentIds.size() > 0">
            <foreach item="item" index="index" collection="speechFragmentIds" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    speech_fragment_id like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
        <if test="businessDirection != null and businessDirection != '' ">
            and business_direction = #{businessDirection}
        </if>
        <if test="speechScene != null and speechScene.size() > 0">
            <foreach item="item" index="index" collection="speechScene" open=" AND (" separator=" OR " close=")">
                speech_scene LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <if test="range != null and range.size() > 0">
            <foreach item="item" index="index" collection="range" open=" AND (" separator=" OR " close=")">
                range LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        order by created_time desc
    </select>
</mapper>