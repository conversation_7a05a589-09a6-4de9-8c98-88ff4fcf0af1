<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechFragmentMapper">
    <sql id="Column_Select">
        tenant_id
        ,revision
        ,created_by
        ,created_time
        ,updated_by
        ,updated_time
        ,id
        ,name
        ,intro
        ,json_text
        ,variables
        ,resource_id
        ,lock_account
        ,lock_name
        ,deleted
        ,flow_file_path
        ,speech_label
        ,fragment_label
        ,status
        ,type
        ,nlp_node_id
        ,publish_file_path
        ,publish_time
        ,publish_by
        ,md5_value
    </sql>

    <sql id="select_result">
        ta.tenant_id
        ,ta.revision
        ,ta.created_by
        ,ta.created_time
        ,ta.updated_by
        ,ta.updated_time
        ,ta.id
        ,ta.name
        ,ta.intro
        ,ta.json_text
        ,ta.variables
        ,ta.resource_id
        ,ta.lock_account
        ,ta.lock_name
        ,ta.deleted
        ,ta.flow_file_path
        ,ta.speech_label
        ,ta.fragment_label
        ,ta.status
        ,ta.type
        ,ta.nlp_node_id
    </sql>

    <select id="selectByParam"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechFragmentDO">
        select <include refid="Column_Select"/>
        from tb_aicc_speech_fragment
        where deleted = 0
        <if test="name != null and name.size() != 0">
            <foreach item="item" index="index" collection="name" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    name like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
        <if test="status != null ">
            and status = #{status}
        </if>
        <if test="type != null ">
            and type = #{type}
        </if>
        <if test="publishFilePathNotNull != null and publishFilePathNotNull">
            and publish_file_path is not null
        </if>
        <if test="id != null and id.size != 0">
            <foreach item="item" index="index" collection="id" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    CAST(id as VARCHAR) = #{item}
                </if>
            </foreach>
        </if>
        <if test="label != null and label.size != 0">
            <foreach item="item" index="index" collection="label" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    fragment_label like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
        <if test="nlpNodeIds != null and nlpNodeIds.size != 0">
            <foreach item="item" index="index" collection="nlpNodeIds" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    nlp_node_id like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
        order by created_time desc
    </select>
    <select id="listFragmentRel" resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechFragmentDO">
        select
        <include refid="select_result"/>
        from tb_aicc_speech_fragment ta
        INNER JOIN (
        SELECT
        speech_fragment_id,
        array_to_string(ARRAY_AGG(speech_id::text), ',') AS speech_id
        FROM tb_aicc_speech_base_fragment_rel
        where deleted = 0
        and rel_state = 1
        GROUP BY speech_fragment_id
        ) tb
        ON ta.id = tb.speech_fragment_id
        where ta.deleted = 0

        <if test="name != null and name.size() != 0">
            <foreach item="item" index="index" collection="name" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    ta.name like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
        order by ta.created_time desc

    </select>
    <select id="countFragmentRel" resultType="java.lang.Integer">

        select
        count(1)
        from tb_aicc_speech_fragment ta
        INNER JOIN (
        SELECT
        speech_fragment_id,
        array_to_string(ARRAY_AGG(speech_id::text), ',') AS speech_id
        FROM tb_aicc_speech_base_fragment_rel
        where deleted = 0
        and rel_state =1
        GROUP BY speech_fragment_id
        ) tb
        ON ta.id = tb.speech_fragment_id
        where ta.deleted = 0
    </select>
    <select id="queryByCondition"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechFragmentDO">
        select <include refid="Column_Select"/>
        from tb_aicc_speech_fragment
        where deleted = 0
        and publish_file_path is not null
        and (

        <foreach item="item" index="index" collection="name" open=" (" separator=" OR " close=")">
            <if test="item != null and item != '' ">
                name like CONCAT('%', #{item}, '%')
            </if>
        </foreach>

        <foreach item="item" index="index" collection="introList" open=" OR (" separator=" OR " close=")">
            <if test="item != null and item != '' ">
                intro like CONCAT('%', #{item}, '%')
            </if>
        </foreach>

        <foreach item="item" index="index" collection="id" open=" OR (" separator=" OR " close=")">
            <if test="item != null and item != '' ">
                CAST(id as VARCHAR) = #{item}
            </if>
        </foreach>

        <if test="label != null and label.size != 0">
            <foreach item="item" index="index" collection="label" open=" OR (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    fragment_label like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
        <if test="nlpNodeIds != null and nlpNodeIds.size != 0">
            <foreach item="item" index="index" collection="nlpNodeIds" open=" OR (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    nlp_node_id like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>

        )
        order by created_time desc
    </select>
    <select id="list" resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechFragmentDO">
        select <include refid="Column_Select"/>
        from tb_aicc_speech_fragment
        where deleted = 0
        <if test="fragment.name != null and fragment.name.size() != 0">
            <foreach item="item" index="index" collection="fragment.name" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    name like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
        <if test="fragment.status != null ">
            and status = #{fragment.status}
        </if>
        <if test="fragment.type != null ">
            and type = #{fragment.type}
        </if>
        <if test="fragment.id != null and fragment.id.size != 0">
            <foreach item="item" index="index" collection="fragment.id" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    CAST(id as VARCHAR) = #{item}
                </if>
            </foreach>
        </if>
        <if test="fragment.label != null and fragment.label.size != 0">
            <foreach item="item" index="index" collection="fragment.label" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    fragment_label like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
        <if test="fragment.nlpNodeIds != null and fragment.nlpNodeIds.size != 0">
            <foreach item="item" index="index" collection="fragment.nlpNodeIds" open=" AND (" separator=" OR " close=")">
                <if test="item != null and item != '' ">
                    nlp_node_id like CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
        and id > #{start}
        order by id asc
        limit 500
    </select>
</mapper>