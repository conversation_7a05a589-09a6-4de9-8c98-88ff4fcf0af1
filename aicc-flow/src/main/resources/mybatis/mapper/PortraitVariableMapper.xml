<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.PortraitVariableMapper">
    <resultMap type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.PortraitVariableDO" id="PatientPortraitVariableMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="configId" column="config_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="required" column="required" jdbcType="INTEGER"/>
        <result property="limitRule" column="limit_rule" jdbcType="VARCHAR"/>
        <result property="internal" column="internal" jdbcType="INTEGER"/>
        <result property="defaultValue" column="default_value" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="DATE"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="DATE"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="column">
        id,config_id,name,code,required,limit_rule,internal,default_value,remark,operator,created_time,updater,updated_time,deleted
    </sql>
    <insert id="batchInsert">
        insert into
        tb_aicc_patient_portrait_variable(
        <include refid="column"/>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},#{item.configId},#{item.name},#{item.code},#{item.required},#{item.limitRule},#{item.internal},
            #{item.defaultValue},#{item.remark},#{item.operator},#{item.createdTime},#{item.updater},#{item.updatedTime},
            #{item.deleted}
            )
        </foreach>

    </insert>
    <update id="batchUpdate">
        update tb_aicc_patient_portrait_variable
        SET
        name =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            WHEN #{item.id} THEN #{item.name}
        </foreach>
        ,code =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            WHEN #{item.id} THEN #{item.code}
        </foreach>
        ,required =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            WHEN #{item.id} THEN #{item.required}
        </foreach>
        ,limit_rule =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            WHEN #{item.id} THEN #{item.limitRule}
        </foreach>
        ,internal =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            WHEN #{item.id} THEN #{item.internal}
        </foreach>
        ,default_value =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            WHEN #{item.id} THEN #{item.defaultValue}
        </foreach>
        ,remark =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            WHEN #{item.id} THEN #{item.remark}
        </foreach>
        ,updater =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            WHEN #{item.id} THEN #{item.updater}
        </foreach>
        ,updated_time = now()
        WHERE
        ID IN
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="PatientPortraitVariableMap">
        select
        <include refid="column"/>
        from tb_aicc_patient_portrait_variable
        where id = #{id}
    </select>

</mapper>