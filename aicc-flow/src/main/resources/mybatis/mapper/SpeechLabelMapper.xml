<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechLabelMapper">






    <insert id="insert">
        insert into tb_aicc_speech_label(
        id, speech_id, code, name, values, tag_type,
        deleted, created_time, updated_time, created_by, updated_by
        )
        values (
        #{id},#{speechId},#{code},#{name},#{values},#{tagType},
        #{deleted},#{createdTime},#{updatedTime},#{createdBy},#{updatedBy}
        )
        on conflict (id) do update set
        id = excluded.id,
        speech_id = excluded.speech_id,
        code = excluded.code,
        name = excluded.name,
        values = excluded.values,
        tag_type = excluded.tag_type,
        deleted = excluded.deleted,
        created_time = excluded.created_time,
        updated_time = excluded.updated_time,
        created_by = excluded.created_by,
        updated_by = excluded.updated_by
    </insert>
</mapper>