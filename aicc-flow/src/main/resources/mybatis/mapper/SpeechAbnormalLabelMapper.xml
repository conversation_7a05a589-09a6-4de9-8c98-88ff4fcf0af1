<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechAbnormalLabelMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelDO">
    <!--@mbg.generated-->
    <!--@Table tb_aicc_speech_abnormal_label-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rel_id" jdbcType="BIGINT" property="relId" />
    <result column="rel_type" jdbcType="INTEGER" property="relType" />
    <result column="logical_operation" jdbcType="VARCHAR" property="logicalOperation" />
    <result column="label_name" jdbcType="VARCHAR" property="labelName" />
    <result column="abnormal_type" jdbcType="VARCHAR" property="abnormalType" />
    <result column="abnormal_reason" jdbcType="VARCHAR" property="abnormalReason" />
    <result column="artificial_intervention" jdbcType="VARCHAR" property="artificialIntervention" />
    <result column="ai_preprocessing" jdbcType="VARCHAR" property="aiPreprocessing" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="sort" jdbcType="BIGINT" property="sort" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, rel_id,rel_type, logical_operation, label_name,abnormal_type, abnormal_reason, artificial_intervention, ai_preprocessing,
    creator, create_time, sort
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from tb_aicc_speech_abnormal_label
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from tb_aicc_speech_abnormal_label
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelDO">
    <!--@mbg.generated-->
    insert into tb_aicc_speech_abnormal_label
    (id, rel_id,rel_type, logical_operation,
    label_name,abnormal_type,abnormal_reason, artificial_intervention, ai_preprocessing, creator, create_time, sort)
    values
    (#{id,jdbcType=BIGINT}, #{rel_id,jdbcType=BIGINT},#{relType,jdbcType=INTEGER}, #{logicalOperation,jdbcType=VARCHAR},
    #{labelName,jdbcType=VARCHAR},#{abnormalType,jdbcType=VARCHAR},#{abnormalReason,jdbcType=VARCHAR},
    #{artificialIntervention,jdbcType=VARCHAR},#{aiPreprocessing,jdbcType=VARCHAR},
    #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{sort,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelDO">
    <!--@mbg.generated-->
    insert into tb_aicc_speech_abnormal_label
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="relId != null">
        rel_id,
      </if>
      <if test="relType != null">
        rel_type,
      </if>
      <if test="logicalOperation != null and logicalOperation != ''">
        logical_operation,
      </if>
      <if test="labelName != null and labelName != ''">
        label_name,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="sort != null">
        sort,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="relId != null">
        #{relId,jdbcType=BIGINT},
      </if>
      <if test="relType != null">
        #{relType,jdbcType=INTEGER},
      </if>
      <if test="logicalOperation != null and logicalOperation != ''">
        #{logicalOperation,jdbcType=VARCHAR},
      </if>
      <if test="labelName != null and labelName != ''">
        #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelDO">
    <!--@mbg.generated-->
    update tb_aicc_speech_abnormal_label
    <set>
      <if test="relId != null">
        rel_id = #{relId,jdbcType=BIGINT},
      </if>
      <if test="relType != null">
        rel_type = #{relType,jdbcType=INTEGER},
      </if>
      <if test="logicalOperation != null and logicalOperation != ''">
        logical_operation = #{logicalOperation,jdbcType=VARCHAR},
      </if>
      <if test="labelName != null and labelName != ''">
        label_name = #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="abnormalType != null and abnormalType != ''">
        abnormal_type = #{abnormalType,jdbcType=VARCHAR},
      </if>
      <if test="abnormalReason != null and abnormalReason != ''">
        abnormal_reason = #{abnormalReason,jdbcType=VARCHAR},
      </if>
      <if test="artificialIntervention != null and artificialIntervention != ''">
        artificial_intervention = #{artificialIntervention,jdbcType=VARCHAR},
      </if>
      <if test="aiPreprocessing != null and aiPreprocessing != ''">
        ai_preprocessing = #{aiPreprocessing,jdbcType=VARCHAR},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelDO">
    <!--@mbg.generated-->
    update tb_aicc_speech_abnormal_label
    set rel_id = #{relId,jdbcType=BIGINT},
    rel_type = #{relType,jdbcType=INTEGER},
    logical_operation = #{logicalOperation,jdbcType=VARCHAR},
    label_name = #{labelName,jdbcType=VARCHAR},
    abnormal_type = #{abnormalType,jdbcType=VARCHAR},
    abnormal_reason = #{abnormalReason,jdbcType=VARCHAR},
    artificial_intervention = #{artificialIntervention,jdbcType=VARCHAR},
    ai_preprocessing = #{aiPreprocessing,jdbcType=VARCHAR},
    creator = #{creator,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    sort = #{sort,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into tb_aicc_speech_abnormal_label
    (
    id, rel_id,rel_type, logical_operation, label_name,
    abnormal_type,abnormal_reason, artificial_intervention, ai_preprocessing,
    creator, create_time, sort
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.relId,jdbcType=BIGINT},#{item.relType,jdbcType=INTEGER},
      #{item.logicalOperation,jdbcType=VARCHAR},
      #{item.labelName,jdbcType=VARCHAR}, #{item.abnormalType,jdbcType=VARCHAR}, #{item.abnormalReason,jdbcType=VARCHAR},
      #{item.artificialIntervention,jdbcType=VARCHAR},#{item.aiPreprocessing,jdbcType=VARCHAR},
      #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.sort,jdbcType=BIGINT})
    </foreach>
    on conflict (id) do update set
    id = excluded.id,
    rel_id = excluded.rel_id,
    rel_type = excluded.rel_type,
    logical_operation = excluded.logical_operation,
    label_name = excluded.label_name,
    abnormal_type = excluded.abnormal_type,
    abnormal_reason = excluded.abnormal_reason,
    artificial_intervention = excluded.artificial_intervention,
    ai_preprocessing = excluded.ai_preprocessing,
    creator = excluded.creator,
    create_time = excluded.create_time,
    sort = excluded.sort;
  </insert>

  <delete id="deleteByRelId">
    delete from tb_aicc_speech_abnormal_label where rel_id = #{relId}
  </delete>

  <select id="selectLabelIdsByRelId" resultType="java.lang.Long">
    select id from tb_aicc_speech_abnormal_label where rel_id = #{relId}
  </select>

  <select id="selectLabelByRelId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_aicc_speech_abnormal_label
    where rel_id = #{relId}
    order by sort
  </select>
    <select id="distinctAbnormalName" resultType="java.lang.String">
      select
      distinct label_name
      from tb_aicc_speech_abnormal_label
      where rel_type = #{relType}
    </select>
</mapper>
