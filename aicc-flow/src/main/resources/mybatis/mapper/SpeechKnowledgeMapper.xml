<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechKnowledgeMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tb_aicc_speech_knowledge
        (
        created_by,
        created_time,
        updated_by,
        updated_time,
        id,
        name,
        description,
        condition,
        type,
        deleted,
        status,
        process_type,
        prompt_content,
        knowledge_type,
        speech_id
        )
        VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (
            #{item.createdBy},
            #{item.createdTime},
            #{item.updatedBy},
            #{item.updatedTime},
            #{item.id},
            #{item.name},
            #{item.description},
            #{item.condition},
            #{item.type},
            #{item.deleted},
            #{item.status},
            #{item.processType},
            #{item.promptContent},
            #{item.knowledgeType},
            #{item.speechId}
            )
        </foreach>
        on conflict (id) do update set
        created_by = excluded.created_by,
        created_time = excluded.created_time,
        updated_by = excluded.updated_by,
        updated_time = excluded.updated_time,
        id = excluded.id,
        name = excluded.name,
        description = excluded.description,
        condition = excluded.condition,
        type = excluded.type,
        deleted = excluded.deleted,
        status = excluded.status,
        process_type = excluded.process_type,
        prompt_content = excluded.prompt_content,
        knowledge_type = excluded.knowledge_type,
        speech_id = excluded.speech_id
    </insert>
    <insert id="insert">
        INSERT INTO tb_aicc_speech_knowledge
        (
        created_by,
        created_time,
        updated_by,
        updated_time,
        id,
        name,
        description,
        condition,
        type,
        deleted,
        status,
        process_type,
        prompt_content,
        knowledge_type,
        speech_id
        )
        VALUES
        (
        #{createdBy},
        #{createdTime},
        #{updatedBy},
        #{updatedTime},
        #{id},
        #{name},
        #{description},
        #{condition},
        #{type},
        #{deleted},
        #{status},
        #{processType},
        #{promptContent},
        #{knowledgeType},
        #{speechId}
        )
        on conflict (id) do update set
        created_by = excluded.created_by,
        created_time = excluded.created_time,
        updated_by = excluded.updated_by,
        updated_time = excluded.updated_time,
        id = excluded.id,
        name = excluded.name,
        description = excluded.description,
        condition = excluded.condition,
        type = excluded.type,
        deleted = excluded.deleted,
        status = excluded.status,
        process_type = excluded.process_type,
        prompt_content = excluded.prompt_content,
        knowledge_type = excluded.knowledge_type,
        speech_id = excluded.speech_id

    </insert>
</mapper>