<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechNlpInterfaceMapper">

    <insert id="batchSave">
        insert into tb_aicc_speech_nlp_interface(tenant_id,revision,created_by,created_time,updated_by,updated_time,id,node_id,speech_id,name,nlp_code)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.tenantId},#{entity.revision},#{entity.createdBy},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedTime},#{entity.id},#{entity.nodeId},#{entity.speechId},#{entity.name},#{entity.nlpCode})
        </foreach>
    </insert>

</mapper>