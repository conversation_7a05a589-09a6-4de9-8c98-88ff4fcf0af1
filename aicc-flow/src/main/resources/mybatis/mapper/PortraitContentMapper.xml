<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.PortraitContentMapper">
    <resultMap type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.PortraitContentDO" id="TbPatinetPortraitContentMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="configId" column="config_id" jdbcType="BIGINT"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="deletedTime" column="deleted_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="column">
        id,config_id,content,created_time,operator,updated_time,updater,deleted_time,deleted
    </sql>
    <insert id="batchInsert">
        INSERT INTO tb_aicc_patient_portrait_content (
        <include refid="column"/>
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.configId},#{item.content},#{item.createdTime},#{item.operator},
            #{item.updatedTime},#{item.updater},#{item.deletedTime},#{item.deleted})
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
        id = excluded.id,
        config_id=excluded.config_id,
        content=excluded.content,
        created_time=excluded.created_time,
        operator=excluded.operator,
        updated_time=excluded.updated_time,
        updater=excluded.updater,
        deleted_time=excluded.deleted_time,
        deleted=excluded.deleted
    </insert>
    <update id="batchUpdate">
        update tb_aicc_patient_portrait_content
        SET
        content =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            WHEN #{item.id} THEN #{item.content}
        </foreach>
        ,updater =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            WHEN #{item.id} THEN #{item.updater}
        </foreach>
        ,updated_time = now()
        WHERE
        ID IN
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>


</mapper>