<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechBaseFragmentRelMapper">

    <resultMap type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechBaseFragmentRelDO" id="SpeechBaseFragmentRelResult">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="speechId" column="speech_id" jdbcType="BIGINT"/>
        <result property="speechFragmentId" column="speech_fragment_id" jdbcType="BIGINT"/>
        <result property="relState" column="rel_state" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>


    <insert id="batchInsert">
        insert into tb_aicc_speech_base_fragment_rel
        (
        id,speech_id,speech_fragment_id,rel_state,created_by,created_time,updated_by,updated_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},#{item.speechId},#{item.speechFragmentId},#{item.relState},
            #{item.createdBy},#{item.createdTime},#{item.updatedBy},#{item.updatedTime}
            )
        </foreach>
    </insert>
</mapper>