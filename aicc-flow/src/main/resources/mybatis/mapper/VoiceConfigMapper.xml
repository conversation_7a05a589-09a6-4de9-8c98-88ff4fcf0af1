<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.VoiceConfigMapper">

    <sql id="Column_List_Insert">
        id
        ,tts_voice_code
        ,tts_voice_name
        ,tts_rate
        ,voicer_pitch
        ,tts_volume
        ,created_by
        ,created_time
        ,updated_by
        ,updated_time
        ,tenant_id
        ,engine_id
        ,voice_type
        ,platform_code
        ,platform_name
        ,org_id
        ,org_name
        ,fork_voice_source
        ,creator_name
    </sql>

    <insert id="insert">
        insert into tb_aicc_voice_config (<include refid="Column_List_Insert"/>)
        values
        (
        #{id}
        ,#{ttsVoiceCode}
        ,#{ttsVoiceName}
        ,#{ttsRate}
        ,#{voicerPitch}
        ,#{ttsVolume}
        ,#{createdBy}
        ,#{createdTime}
        ,#{updatedBy}
        ,#{updatedTime}
        ,#{tenantId}
        ,#{engineId}
        ,#{voiceType}
        ,#{platformCode}
        ,#{platformName}
        ,#{orgId}
        ,#{orgName}
        ,#{forkVoiceSource}
        ,#{creatorName}
        )
        on conflict (id) do update set
        id = excluded.id,
        tts_voice_code = excluded.tts_voice_code,
        tts_voice_name = excluded.tts_voice_name,
        tts_rate = excluded.tts_rate,
        voicer_pitch = excluded.voicer_pitch,
        tts_volume = excluded.tts_volume,
        updated_by = excluded.updated_by,
        updated_time = excluded.updated_time,
        tenant_id = excluded.tenant_id,
        engine_id = excluded.engine_id,
        voice_type = excluded.voice_type,
        platform_code = excluded.platform_code,
        platform_name = excluded.platform_name,
        org_id = excluded.org_id,
        org_name = excluded.org_name,
        fork_voice_source = excluded.fork_voice_source,
        creator_name = excluded.creator_name
    </insert>

    <select id="forkPlatformSelect"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.FilterSelectDO">
        select distinct platform_code as id, platform_name as name from tb_aicc_voice_config where voice_type = 1
        and deleted = 0 and platform_code is not null and platform_code != '0'
    </select>
    <select id="forkOrgSelect"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.FilterSelectDO">
        select distinct org_id as id, org_name as name from tb_aicc_voice_config where voice_type = 1 and deleted = 0 and org_id is not null
    </select>

</mapper>