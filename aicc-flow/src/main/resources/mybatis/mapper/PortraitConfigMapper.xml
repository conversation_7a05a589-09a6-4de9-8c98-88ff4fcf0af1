<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.PortraitConfigMapper">

    <sql id="Base_Column_List">
        id, parent_id, name, code, created_time, operator, updated_time, updater, deleted_time, deleted
    </sql>
    <resultMap type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.PortraitConfigDO" id="PortraitConfigMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="deletedTime" column="deleted_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <collection property="children"
                      ofType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.PortraitConfigDO"
                      column="id"
                      select="configTree">
        </collection>
    </resultMap>


    <select id="configList"
            resultMap="PortraitConfigMap">
        select <include refid="Base_Column_List"/>
        from tb_aicc_patient_portrait_config
        where deleted = 0
        and parent_id = #{parentId}
        order by created_time desc
    </select>

    <select id="configTree"
            resultMap="PortraitConfigMap">
    select <include refid="Base_Column_List"/>
        from tb_aicc_patient_portrait_config
        where deleted = 0
        and parent_id = #{id}
        order by created_time desc
    </select>

    <select id="checkNameRepeat" resultType="java.lang.Integer">
        select count(1) from tb_aicc_patient_portrait_config
        where deleted = 0
        and name = #{name}
        and parent_id = #{parentId}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insert">
        insert into tb_aicc_patient_portrait_config
        (
        id, parent_id, name, code, created_time, operator,
        updated_time, updater, deleted_time, deleted
        )
        values
        (
        #{id}, #{parentId}, #{name}, #{code}, #{createdTime}, #{operator},
        #{updatedTime}, #{updater}, #{deletedTime}, #{deleted}
        )
        on conflict (id) do update set
        id = excluded.id,
        parent_id = excluded.parent_id,
        name = excluded.name,
        code = excluded.code,
        created_time = excluded.created_time,
        operator = excluded.operator,
        updated_time = excluded.updated_time,
        updater = excluded.updater,
        deleted_time = excluded.deleted_time,
        deleted = excluded.deleted
    </insert>
</mapper>
