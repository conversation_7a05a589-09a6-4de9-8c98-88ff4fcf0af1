<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechVariableMapper">
    <sql id="Column_List_Insert">
        tenant_id
        ,revision
        ,created_by
        ,id
        ,speech_id
        ,node_id
        ,name
        ,code
        ,required
        ,limit_rule
        ,internal
        ,default_value
        ,remark
    </sql>
    <insert id="batchSave">
        insert into tb_aicc_speech_variable (<include refid="Column_List_Insert"/>)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.tenantId}
            ,#{item.revision}
            ,#{item.createdBy}
            ,#{item.id}
            ,#{item.speechId}
            ,#{item.nodeId}
            ,#{item.name}
            ,#{item.code}
            ,#{item.required}
            ,#{item.limitRule}
            ,#{item.internal}
            ,#{item.defaultValue}
            ,#{item.remark}
            )
        </foreach>
    </insert>


    <select id="getBySpeechId" resultType="java.lang.String">
        select name from tb_aicc_speech_variable where speech_id = #{speechId} and deleted=0
    </select>
</mapper>