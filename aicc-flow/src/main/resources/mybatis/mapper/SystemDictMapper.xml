<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SystemDictMapper">

    <sql id="Column_Insert">
        id
        ,dict_name
        ,dict_value
        ,pid
        ,dict_flag
        ,creator
        ,updater
        ,rank_id
        ,remark
        ,general_field1
        ,general_field2
        ,general_field3
        ,create_time
        ,update_time
    </sql>

    <sql id="Column_List">
        id
        ,dict_name
        ,dict_value
        ,pid
        ,dict_flag
        ,create_time
        ,creator
        ,update_time
        ,updater
        ,rank_id
        ,remark
        ,deleted
        ,general_field1
        ,general_field2
        ,general_field3
    </sql>

    <select id="getFatherDict" resultType="com.iflytek.aicc.flow.api.vo.DictPackageVO">
        SELECT
        <include refid="Column_List"/>
        FROM tb_aicc_system_dict WHERE deleted = 0
        <if test="pid != null">
            AND pid = #{pid}
        </if>
        <if test="dictValue !=null and dictValue!=''">
            AND dict_value = #{dictValue}
        </if>
    </select>

    <select id="getDictList" resultType="com.iflytek.aicc.flow.api.vo.DictPackageVO">
        SELECT
        <include refid="Column_List"/>
        FROM tb_aicc_system_dict WHERE deleted = 0
        <if test="isParent">
            AND pid = -1
        </if>
        <if test="!isParent">
            AND pid != -1
        </if>
    </select>

    <select id="getChildByPid" resultType="com.iflytek.aicc.flow.api.vo.DictPackageVO">
        SELECT
        <include refid="Column_List"/>
        FROM tb_aicc_system_dict WHERE deleted = 0 AND pid != -1 AND pid = #{pid} order by rank_id asc
    </select>

    <select id="querySystemDictList" resultType="com.iflytek.aicc.flow.api.vo.DictPackageVO">
        SELECT
        <include refid="Column_List"/>
        FROM tb_aicc_system_dict WHERE deleted = 0 AND pid = -1
        <if test="param.id != null">
            AND id = #{param.id}
        </if>
        <if test="param.parentName != null and param.parentName != ''">
            AND dict_name like concat('%',#{param.parentName},'%')
        </if>
        <if test="param.parentValue != null and param.parentValue != ''">
            AND dict_value like concat('%',#{param.parentValue},'%')
        </if>
        <if test="param.childName != null and param.childName != ''">
            AND id IN (<include refid="queryPidByChildName"/>)
        </if>
        ORDER BY rank_id asc,create_time DESC
    </select>

    <sql id="queryPidByChildName">
        SELECT DISTINCT pid FROM tb_aicc_system_dict WHERE deleted = 0 AND pid > 0 AND dict_name LIKE
        concat('%',#{param.childName},'%')
    </sql>

    <select id="checkNameRepeat" resultType="java.lang.Integer">
        select count(1) from tb_aicc_system_dict
        where (dict_name = #{name}) and pid = #{parentId} AND deleted = 0
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <select id="getById" resultType="com.iflytek.aicc.flow.domain.manage.entity.SystemDictEntity">
        SELECT
        <include refid="Column_List"/>
        FROM tb_aicc_system_dict WHERE deleted = 0 AND id =#{id}
    </select>

    <insert id="insert">
        INSERT INTO tb_aicc_system_dict (<include refid="Column_Insert"/>)
        VALUES (
        #{id},
        #{dictName},
        #{dictValue},
        #{pid},
        #{dictFlag},
        #{creator},
        #{updater},
        #{rankId},
        #{remark},
        #{generalField1},
        #{generalField2},
        #{generalField3},
        now(),
        now()
        )
        on conflict (id) do update set
        id = excluded.id,
        dict_name = excluded.dict_name,
        dict_value = excluded.dict_value,
        pid = excluded.pid,
        dict_flag = excluded.dict_flag,
        creator = excluded.creator,
        updater = excluded.updater,
        rank_id = excluded.rank_id,
        remark = excluded.remark,
        general_field1 = excluded.general_field1,
        general_field2 = excluded.general_field2,
        general_field3 = excluded.general_field3,
        create_time = excluded.create_time,
        update_time = now()
    </insert>

    <update id="update">
        UPDATE tb_aicc_system_dict
        <set>
            dict_name = #{dictName},
            dict_value = #{dictValue},
            pid = #{pid},
            dict_flag = #{dictFlag},
            updater = #{updater},
            rank_id = #{rankId},
            remark = #{remark},
            general_field1 = #{generalField1},
            general_field2 = #{generalField2},
            general_field3 = #{generalField3}
        </set>
        WHERE id=#{id}
    </update>

    <update id="delete" parameterType="map">
        UPDATE tb_aicc_system_dict SET deleted = 1, updater = #{updater} WHERE deleted = 0
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="pid != null">
            AND (pid = #{pid} or id = #{pid})
        </if>
    </update>

    <select id="getDictSelect" resultType="com.iflytek.aicc.flow.api.vo.DictSelectVO">
        select id, dict_name as name, dict_value as value
        from tb_aicc_system_dict where dict_flag = 1 and pid = (select id from tb_aicc_system_dict WHERE pid = -1 and
        dict_value = #{dictValue})
        and deleted=0
        <if test="dictFlag != null">
            AND dict_flag = #{dictFlag}
        </if>
    </select>

    <select id="getLikeLabelIds" resultType="java.lang.String">
        select id from tb_aicc_system_dict where
        deleted = 0
        and pid != -1
        <if test="textList != null and textList.size() > 0">
            <foreach item="item" index="index" collection="textList" open=" AND (" separator=" OR " close=")">
                <if test="item != '' and item != null ">
                    dict_name LIKE CONCAT('%', #{item}, '%')
                </if>
            </foreach>
        </if>
    </select>

    <select id="getFixedLabel" resultType="com.iflytek.aicc.flow.api.vo.DictSelectVO">
        select id, dict_name as name, dict_value as value
        from tb_aicc_system_dict where dict_flag = 1 and deleted=0
        <choose>
            <when test="type != null and type == 1">
                and rank_id = 99
            </when>
            <otherwise>
                and rank_id != 99
            </otherwise>
        </choose>
        <if test="list != null and list.size() > 0">
            <foreach item="item" index="index" collection="list" open=" AND (" separator=" OR " close=")">
                pid = #{item}
            </foreach>
        </if>
        order by rank_id asc,create_time desc

    </select>

</mapper>
