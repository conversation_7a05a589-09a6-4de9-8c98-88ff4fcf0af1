<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechAbnormalLabelItemMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelItemDO">
    <!--@mbg.generated-->
    <!--@Table tb_aicc_speech_abnormal_label_item-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="label_id" jdbcType="BIGINT" property="labelId" />
    <result column="interface_id" jdbcType="BIGINT" property="interfaceId" />
    <result column="field_id" jdbcType="BIGINT" property="fieldId" />
    <result column="field" jdbcType="VARCHAR" property="field" />
    <result column="compare_code" jdbcType="VARCHAR" property="compareCode" />
    <result column="compare_content_name" jdbcType="VARCHAR" property="compareContentName" />
    <result column="compare_content" jdbcType="VARCHAR" property="compareContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="sort" jdbcType="BIGINT" property="sort" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, label_id, interface_id, field_id, field, compare_code, compare_content_name, 
    compare_content, create_time, creator, sort
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_aicc_speech_abnormal_label_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from tb_aicc_speech_abnormal_label_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelItemDO">
    <!--@mbg.generated-->
    insert into tb_aicc_speech_abnormal_label_item (id, label_id, interface_id, 
      field_id, field, compare_code, 
      compare_content_name, compare_content, create_time, 
      creator, sort)
    values (#{id,jdbcType=BIGINT}, #{labelId,jdbcType=BIGINT}, #{interfaceId,jdbcType=BIGINT}, 
      #{fieldId,jdbcType=BIGINT}, #{field,jdbcType=VARCHAR}, #{compareCode,jdbcType=VARCHAR}, 
      #{compareContentName,jdbcType=VARCHAR}, #{compareContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{sort,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelItemDO">
    <!--@mbg.generated-->
    insert into tb_aicc_speech_abnormal_label_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="labelId != null">
        label_id,
      </if>
      <if test="interfaceId != null">
        interface_id,
      </if>
      <if test="fieldId != null">
        field_id,
      </if>
      <if test="field != null and field != ''">
        field,
      </if>
      <if test="compareCode != null and compareCode != ''">
        compare_code,
      </if>
      <if test="compareContentName != null and compareContentName != ''">
        compare_content_name,
      </if>
      <if test="compareContent != null and compareContent != ''">
        compare_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="sort != null">
        sort,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labelId != null">
        #{labelId,jdbcType=BIGINT},
      </if>
      <if test="interfaceId != null">
        #{interfaceId,jdbcType=BIGINT},
      </if>
      <if test="fieldId != null">
        #{fieldId,jdbcType=BIGINT},
      </if>
      <if test="field != null and field != ''">
        #{field,jdbcType=VARCHAR},
      </if>
      <if test="compareCode != null and compareCode != ''">
        #{compareCode,jdbcType=VARCHAR},
      </if>
      <if test="compareContentName != null and compareContentName != ''">
        #{compareContentName,jdbcType=VARCHAR},
      </if>
      <if test="compareContent != null and compareContent != ''">
        #{compareContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelItemDO">
    <!--@mbg.generated-->
    update tb_aicc_speech_abnormal_label_item
    <set>
      <if test="labelId != null">
        label_id = #{labelId,jdbcType=BIGINT},
      </if>
      <if test="interfaceId != null">
        interface_id = #{interfaceId,jdbcType=BIGINT},
      </if>
      <if test="fieldId != null">
        field_id = #{fieldId,jdbcType=BIGINT},
      </if>
      <if test="field != null and field != ''">
        field = #{field,jdbcType=VARCHAR},
      </if>
      <if test="compareCode != null and compareCode != ''">
        compare_code = #{compareCode,jdbcType=VARCHAR},
      </if>
      <if test="compareContentName != null and compareContentName != ''">
        compare_content_name = #{compareContentName,jdbcType=VARCHAR},
      </if>
      <if test="compareContent != null and compareContent != ''">
        compare_content = #{compareContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelItemDO">
    <!--@mbg.generated-->
    update tb_aicc_speech_abnormal_label_item
    set label_id = #{labelId,jdbcType=BIGINT},
      interface_id = #{interfaceId,jdbcType=BIGINT},
      field_id = #{fieldId,jdbcType=BIGINT},
      field = #{field,jdbcType=VARCHAR},
      compare_code = #{compareCode,jdbcType=VARCHAR},
      compare_content_name = #{compareContentName,jdbcType=VARCHAR},
      compare_content = #{compareContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into tb_aicc_speech_abnormal_label_item
    (id, label_id, interface_id, field_id, field, compare_code, compare_content_name, 
      compare_content, create_time, creator, sort)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.labelId,jdbcType=BIGINT}, #{item.interfaceId,jdbcType=BIGINT}, 
        #{item.fieldId,jdbcType=BIGINT}, #{item.field,jdbcType=VARCHAR}, #{item.compareCode,jdbcType=VARCHAR}, 
        #{item.compareContentName,jdbcType=VARCHAR}, #{item.compareContent,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.sort,jdbcType=BIGINT}
        )
    </foreach>
    on conflict (id) do update set
    id = excluded.id,
    label_id = excluded.label_id,
    interface_id = excluded.interface_id,
    field_id = excluded.field_id,
    field = excluded.field,
    compare_code = excluded.compare_code,
    compare_content_name = excluded.compare_content_name,
    compare_content = excluded.compare_content,
    create_time = excluded.create_time,
    creator = excluded.creator,
    sort = excluded.sort;
  </insert>

  <delete id="deleteByLabelIds">
    delete from tb_aicc_speech_abnormal_label_item where label_id in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </delete>

  <select id="selectByLabelId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_aicc_speech_abnormal_label_item
    where label_id = #{labelId}
    order by sort
  </select>

  <select id="selectByLabelIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_aicc_speech_abnormal_label_item
    where label_id in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>