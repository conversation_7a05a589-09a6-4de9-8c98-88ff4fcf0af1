<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.EngineConfigMapper">

    <resultMap type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.EngineConfigDO"
               id="TbAiccEngineConfigMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="engineName" column="engine_name" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="serviceName" column="service_name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="engineIntro" column="engine_intro" jdbcType="VARCHAR"/>
        <result property="defaultEngine" column="default_engine" jdbcType="INTEGER"/>
    </resultMap>


    <insert id="insert">
        insert into tb_aicc_engine_config(
        id,engine_name,creator,created_time,updater,deleted,
        tenant_id,service_name,type,engine_intro,default_engine
        )
        values
        (#{id},#{engineName},#{creator},#{createdTime},#{updater},#{deleted},
        #{tenantId},#{serviceName},#{type},#{engineIntro},#{defaultEngine}
        )
        on conflict (id) do update set
        id = excluded.id,
        engine_name=excluded.engine_name,
        creator=excluded.creator,
        created_time=excluded.created_time,
        updater=excluded.updater,
        deleted=excluded.deleted,
        tenant_id=excluded.tenant_id,
        service_name=excluded.service_name,
        type=excluded.type,
        engine_intro=excluded.engine_intro,
        default_engine=excluded.default_engine
    </insert>
</mapper>
