<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechVoiceMapper">

    <insert id="batchSave">
        insert into tb_aicc_speech_voice(tenant_id,revision,created_by,created_time,updated_by,updated_time,id,speech_id,node_id,name,voice_content,voice_url)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.tenantId},#{entity.revision},#{entity.createdBy},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedTime},#{entity.id},#{entity.speechId},#{entity.nodeId},#{entity.name},#{entity.voiceContent},#{entity.voiceUrl})
        </foreach>
    </insert>

</mapper>