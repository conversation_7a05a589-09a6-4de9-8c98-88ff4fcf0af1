<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.LlmTemplateMapper">

    <resultMap type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.LlmTemplateDO" id="llmPromptTemplateMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="templateContent" column="template_content" jdbcType="VARCHAR"/>
        <result property="variables" column="variables" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="defaultFlag" column="default_flag" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="templateType" column="template_type" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Column_Insert">
        id, name, template_content, variables, description, default_flag,
        tenant_id, revision, created_by, created_time, updated_by, updated_time, template_type
    </sql>
    <select id="getLlmTemplateList"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.LlmTemplateDO">
        select
        <include refid="Column_Insert"/>
        from tb_aicc_llm_prompt_template
        <where>
            <if test="name != null and name != ''">
                name like concat('%', #{name}, '%')
            </if>
            <if test="templateType != null">
                and template_type = #{templateType}
            </if>
        </where>
        order by created_time desc
    </select>
    <select id="getLlmTemplateByType"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.LlmTemplateDO">
        select
        <include refid="Column_Insert"/>
        from tb_aicc_llm_prompt_template
        where template_type = #{templateType}
        limit 1
    </select>
    <select id="queryByName"
            resultType="com.iflytek.aicc.flow.infrastructure.repository.dataobject.LlmTemplateDO">
        select
        <include refid="Column_Insert"/>
        from tb_aicc_llm_prompt_template
        where name = #{name}
        limit 1
    </select>
</mapper>
