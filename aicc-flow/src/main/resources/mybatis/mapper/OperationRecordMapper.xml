<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.OperationRecordMapper">
    <sql id="Column_Insert">
        ID
        ,TYPE
        ,REL_ID
        ,OP_TYPE
        ,IP
        ,RECORD
        ,OPERATOR
        ,record_revision
    </sql>
    <insert id="batchInsert">
        insert into tb_aicc_operation_record (<include refid="Column_Insert"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id}
            ,#{item.type}
            ,#{item.relId}
            ,#{item.opType}
            ,#{item.ip}
            ,#{item.record}
            ,#{item.operator}
            ,#{item.recordRevision}
            )
        </foreach>

    </insert>
    <resultMap id="resultMap" type="com.iflytek.aicc.flow.infrastructure.repository.dataobject.OperationRecordDo">
        <result column="rel_id" property="relId"/>
        <result column="recordRevision" property="recordRevision"/>
    </resultMap>
    <select id="getMaxVersion" resultMap="resultMap">
        select max(record_revision) as recordRevision,
        rel_id
        from tb_aicc_operation_record
        where rel_id in
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.relId}
            )
        </foreach>
        group by rel_id
    </select>
</mapper>