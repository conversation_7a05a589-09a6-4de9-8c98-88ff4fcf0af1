<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">
    <springProperty scope="context" name="logging.file.maxSize" source="logging.file.maxSize" defaultValue="500MB"/>
    <springProperty scope="context" name="spring.application.name" source="spring.application.name"/>
    <property name="LOG_FILE_MAX_SIZE" value="${logging.file.maxSize}"/>
    <property name="LOG_PATTERN" value="${LOG_PATTERN:-%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{36} %X{tid} type:%X{type:-1} uid:%X{uid} sm:%X{summary} code:%X{code} x0:%X{x0} x1:%X{x1} x2:%X{x2} x3:%t x4:%X{x4} x5:%X{x5} n0:%X{n0} n1:%X{n1} n2:%X{n2} n3:%X{n3} : %msg%n}"/>
    <property name="LOG_PATH" value="${LOG_PATH:-${logPath:-/data/logs/${spring.application.name}}}"/>
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{36} %X{tid} type:%X{type:-1} uid:%X{uid} sm:%X{summary} code:%X{code} x0:%X{x0} x1:%X{x1} x2:%X{x2} x3:%thread x4:%X{x4} x5:%X{x5} n0:%X{n0} n1:%X{n1} n2:%X{n2} n3:%X{n3} : %msg%n"/>
    <include resource="com/iflytek/medicalboot/logging/logback-base.xml"/>
</configuration>