package com.iflytek.aicc.flow.infrastructure.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;


/**
 * @author: hengzhao4
 * @date: 2025/1/9 16:12
 * @description 异常标签条目表
 */


@Data
@TableName("tb_aicc_speech_abnormal_label_item")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpeechAbnormalLabelItemDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 接口id
     */
    private Long interfaceId;

    /**
     * 字段名id
     */
    private Long fieldId;

    /**
     * 字段名
     */
    private String field;

    /**
     * 比较符
     */
    private String compareCode;

    /**
     * 比较文本展示
     */
    private String compareContentName;

    /**
     * 比较文本或编码
     */
    private String compareContent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 排序
     */
    private Long sort;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SpeechAbnormalLabelItemDO that = (SpeechAbnormalLabelItemDO) o;
        return Objects.equals(labelId, that.labelId) &&
                Objects.equals(interfaceId, that.interfaceId) && Objects.equals(field, that.field) &&
                Objects.equals(fieldId, that.fieldId) &&
                Objects.equals(compareCode, that.compareCode) && Objects.equals(compareContent, that.compareContent)
                && Objects.equals(compareContentName, that.compareContentName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(labelId, interfaceId, field, fieldId, compareCode, compareContent, compareContentName);
    }
}