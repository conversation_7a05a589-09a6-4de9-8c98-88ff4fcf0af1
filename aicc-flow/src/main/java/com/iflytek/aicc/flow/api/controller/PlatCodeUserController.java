package com.iflytek.aicc.flow.api.controller;

import com.iflytek.aicc.flow.api.param.PlatCodeUserAddParam;
import com.iflytek.aicc.flow.api.param.PlatCodeUserDeleteParam;
import com.iflytek.aicc.flow.api.param.PlatCodeUserModifyParam;
import com.iflytek.aicc.flow.api.param.PlatCodeUserQueryListParam;
import com.iflytek.aicc.flow.api.param.PlatFormListQueryParam;
import com.iflytek.aicc.flow.api.vo.PlatCodeUserVO;
import com.iflytek.aicc.flow.api.vo.PlatFormListVO;
import com.iflytek.aicc.flow.application.service.PlatCodeUserService;
import com.iflytek.fpva.uapcenter.pojo.UapUser;
import com.iflytek.fpva.uapcenter.pojo.UserRoleSearchParam;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: zhao heng
 * @date: 2025-06-18 10:03
 * @description:
 */
@Api(tags = "交付管理")
@RequestMapping("v1/pt/delivery")
@RestController
@Slf4j
public class PlatCodeUserController {

    private final PlatCodeUserService platCodeUserService;


    public PlatCodeUserController(PlatCodeUserService platCodeUserService) {
        this.platCodeUserService = platCodeUserService;
    }


    @GetMapping("out-transfer/list")
    @ApiOperation("私有化平台列表")
    public List<PlatFormListVO> outTransferList(PlatFormListQueryParam param) {
        return platCodeUserService.outTransferList(param);
    }

    @GetMapping("user/list")
    @ApiOperation("用户中心交付用户列表")
    public PageList<UapUser> userList(UserRoleSearchParam userSearchParam, String loginUserId, Page page) {
        return platCodeUserService.userList(userSearchParam, loginUserId, page);
    }

    @GetMapping("list")
    @ApiOperation("已配置交付列表")
    public PageList<PlatCodeUserVO> platCodeUserList(PlatCodeUserQueryListParam param, Page page) {
        return platCodeUserService.platCodeUserList(param, page);
    }

    @PostMapping("delete")
    @ApiOperation("删除交付角色")
    public Boolean deletePlatCodeUser(@Valid @RequestBody PlatCodeUserDeleteParam param, String loginUserId) {
        return platCodeUserService.deletePlatCodeUser(param, loginUserId);
    }

    @PostMapping("add")
    @ApiOperation("新增交付角色")
    public Boolean addPlatCodeUser(@Valid @RequestBody PlatCodeUserAddParam param, String loginUserId) {
        return platCodeUserService.addPlatCodeUser(param, loginUserId);
    }

    @PostMapping("modify")
    @ApiOperation("修改交付角色")
    public Boolean modifyPlatCodeUser(@Valid @RequestBody PlatCodeUserModifyParam param, String loginUserId) {
        return platCodeUserService.modifyPlatCodeUser(param, loginUserId);
    }
}
