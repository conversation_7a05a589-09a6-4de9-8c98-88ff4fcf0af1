package com.iflytek.aicc.flow.domain.driven.entity.link;

import com.iflytek.aicc.flow.application.cache.FlowSession;
import com.iflytek.aicc.flow.application.cache.ThreadSessionContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;


/**
 * 类说明 正则表达式规则校验
 *
 * <AUTHOR>
 * @date 2023/8/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class RegularLink extends BaseLink {

    /**
     * 规则表达式
     */
    private String condition;

    @Override
    public Boolean judgeLink() {
        //QLExpress规则表达式判断
        try {
            FlowSession session = ThreadSessionContext.getSession();
            String value = session.getInvokeMessage().getContent();
            if (StringUtils.isBlank(value)) {
                return false;
            }
            return Pattern.matches(condition, value);
        } catch (Exception e) {
            log.error("正则:[{}]校验异常", this.condition, e);
            return false;
        }
    }

}
