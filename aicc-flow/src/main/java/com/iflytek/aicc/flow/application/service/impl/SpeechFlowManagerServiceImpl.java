package com.iflytek.aicc.flow.application.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.iflytek.aicc.common.constants.RedisCacheKey;
import com.iflytek.aicc.common.infrastructure.redis.RedisCacheService;
import com.iflytek.aicc.common.infrastructure.redis.RedissonService;
import com.iflytek.aicc.common.utils.AesEncryptUtil;
import com.iflytek.aicc.common.utils.CommonUtil;
import com.iflytek.aicc.common.utils.FileDownloadUtils;
import com.iflytek.aicc.common.utils.FileUploadUtils;
import com.iflytek.aicc.common.utils.ZipFileUtils;
import com.iflytek.aicc.flow.api.param.PlatFormListQueryParam;
import com.iflytek.aicc.flow.api.param.SpeechAbnormalLabelItemParam;
import com.iflytek.aicc.flow.api.param.SpeechAbnormalLabelParam;
import com.iflytek.aicc.flow.api.param.SpeechAbnormalLabelSaveParam;
import com.iflytek.aicc.flow.api.param.SpeechFlowSyncParam;
import com.iflytek.aicc.flow.api.param.SpeechKnowledgeParam;
import com.iflytek.aicc.flow.api.param.SpeechLockOperateParam;
import com.iflytek.aicc.flow.api.param.SpeechManagerCopyParam;
import com.iflytek.aicc.flow.api.vo.DictPackageVO;
import com.iflytek.aicc.flow.api.vo.PlatFormListVO;
import com.iflytek.aicc.flow.api.vo.SpeechFragmentSimpleVO;
import com.iflytek.aicc.flow.api.vo.SpeechNodeInterfaceVO;
import com.iflytek.aicc.flow.application.cache.FlowCacheManager;
import com.iflytek.aicc.flow.application.cache.FlowSession;
import com.iflytek.aicc.flow.application.convertor.SpeechManagerConvertor;
import com.iflytek.aicc.flow.application.dto.InvokeMessageDto;
import com.iflytek.aicc.flow.application.dto.MatchNodeExportDto;
import com.iflytek.aicc.flow.application.dto.SpeechAnalysisExportDto;
import com.iflytek.aicc.flow.application.dto.SpeechFlowDetailDto;
import com.iflytek.aicc.flow.application.dto.SpeechFlowDto;
import com.iflytek.aicc.flow.application.dto.SpeechLinkExplainDto;
import com.iflytek.aicc.flow.application.dto.SpeechManagerExportDto;
import com.iflytek.aicc.flow.application.dto.SpeechManagerUpdateDto;
import com.iflytek.aicc.flow.application.dto.SpeechReleaseDto;
import com.iflytek.aicc.flow.application.listener.event.FlowCacheEvent;
import com.iflytek.aicc.flow.application.listener.event.OperationRecordEvent;
import com.iflytek.aicc.flow.application.service.SpeechFlowConfigSaveService;
import com.iflytek.aicc.flow.application.service.SpeechFlowManagerService;
import com.iflytek.aicc.flow.application.service.SpeechVariableService;
import com.iflytek.aicc.flow.application.service.SystemDictService;
import com.iflytek.aicc.flow.common.Constants;
import com.iflytek.aicc.flow.common.HierarchyUtil;
import com.iflytek.aicc.flow.common.OperationRecordConstants;
import com.iflytek.aicc.flow.common.enums.FlowLabelEnum;
import com.iflytek.aicc.flow.common.enums.PlayNodeEnum;
import com.iflytek.aicc.flow.common.enums.PublishRecordEnums;
import com.iflytek.aicc.flow.common.enums.SpeechFragmentRelEnums;
import com.iflytek.aicc.flow.common.enums.SpeechManagerEnums;
import com.iflytek.aicc.flow.domain.driven.entity.CommonEnd;
import com.iflytek.aicc.flow.domain.driven.entity.FlowBaseConfig;
import com.iflytek.aicc.flow.domain.driven.entity.NodeNlp;
import com.iflytek.aicc.flow.domain.driven.entity.NodePrompt;
import com.iflytek.aicc.flow.domain.driven.entity.NodeSetting;
import com.iflytek.aicc.flow.domain.driven.entity.NodeTag;
import com.iflytek.aicc.flow.domain.driven.entity.link.ConditionLink;
import com.iflytek.aicc.flow.domain.driven.entity.node.AnyMatchNode;
import com.iflytek.aicc.flow.domain.driven.entity.node.Node;
import com.iflytek.aicc.flow.domain.driven.entity.node.PlayNode;
import com.iflytek.aicc.flow.domain.driven.entity.node.PlayRespondNode;
import com.iflytek.aicc.flow.domain.driven.entity.node.StartNode;
import com.iflytek.aicc.flow.domain.driven.entity.variable.BaseVariable;
import com.iflytek.aicc.flow.domain.driven.repository.llm.LlmServiceFactory;
import com.iflytek.aicc.flow.domain.driven.repository.llm.bo.LlmRequestBO;
import com.iflytek.aicc.flow.domain.driven.repository.llm.bo.LlmResultBO;
import com.iflytek.aicc.flow.domain.manage.bo.AsyncResultVo;
import com.iflytek.aicc.flow.domain.manage.bo.ExportResultBO;
import com.iflytek.aicc.flow.domain.manage.bo.SpeechManagerBO;
import com.iflytek.aicc.flow.domain.manage.bo.SpeechManagerExportBO;
import com.iflytek.aicc.flow.domain.manage.bo.SpeechManagerListBO;
import com.iflytek.aicc.flow.domain.manage.entity.LlmTemplateEntity;
import com.iflytek.aicc.flow.domain.manage.entity.PlatCodeUserEntity;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechBaseEntity;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechBaseFragmentRelEntity;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechConfigEntity;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechFragmentEntity;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechKnowledgeEntity;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechManagerExportEntity;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechPublishRecordEntity;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechSceneConfigEntity;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechVariableEntity;
import com.iflytek.aicc.flow.domain.manage.entity.VoiceConfigEntity;
import com.iflytek.aicc.flow.domain.manage.repository.LlmTemplateRepository;
import com.iflytek.aicc.flow.domain.manage.repository.PlatCodeUserRepository;
import com.iflytek.aicc.flow.domain.manage.repository.PublishRecordRepository;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechBaseFragmentRelRepository;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechBaseRepository;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechConfigRepository;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechFragmentRepository;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechKnowledgeRepository;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechSceneConfigRepository;
import com.iflytek.aicc.flow.infrastructure.config.FlowConfig;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechFragmentDO;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechManagerListDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechFragmentMapper;
import com.iflytek.aicc.flow.infrastructure.rpc.aiccflow.AiccFlowService;
import com.iflytek.aicc.flow.infrastructure.rpc.iris.NlpInterfaceService;
import com.iflytek.aicc.flow.infrastructure.rpc.iris.dto.NlpInterfaceDTO;
import com.iflytek.aicc.flow.infrastructure.rpc.iris.dto.NlpInterfaceFieldDTO;
import com.iflytek.aicc.flow.infrastructure.rpc.iris.dto.NlpInterfaceFieldDetailDTO;
import com.iflytek.aicc.flow.infrastructure.rpc.iris.dto.NlpNodeListParam;
import com.iflytek.aicc.flow.infrastructure.rpc.iris.dto.NlpNodeResponse;
import com.iflytek.aicc.flow.infrastructure.rpc.iris.dto.SpeechAbnormalLabelSaveRequest;
import com.iflytek.aicc.flow.infrastructure.rpc.iris.dto.SpeechBusinessRelEntityRequest;
import com.iflytek.aicc.flow.infrastructure.rpc.iris.dto.SpeechCraftRequest;
import com.iflytek.aicc.flow.infrastructure.rpc.llm.LlmService;
import com.iflytek.fpva.uapcenter.pojo.UapUser;
import com.iflytek.fpva.uapcenter.pojo.UserRoleSearchParam;
import com.iflytek.fpva.uapcenter.service.UserOrgQueryApi;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.UidService;
import com.iflytek.medicalboot.core.s3.S3Properties;
import com.iflytek.outbound.sdk.constants.ContainerParamEnum;
import com.iflytek.outbound.utils.DateUtil;
import com.iflytek.outbound.utils.JacksonUtils;
import com.iflytek.outbound.utils.easyUtil.CommonAbstractVerticalCellStyleStrategy;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.iflytek.aicc.flow.common.Constants.AI_MODEL_NODE_GENERATE;
import static com.iflytek.aicc.flow.common.Constants.FLOW_FILENAME_SPLIT;
import static com.iflytek.aicc.flow.common.Constants.FLOW_FILEPATH_SPLIT;
import static com.iflytek.aicc.flow.common.Constants.FLOW_FILE_PREFIX;
import static com.iflytek.aicc.flow.common.enums.SpeechManagerEnums.SpeechPublishOperationEnum.STATUS_DELETE;
import static com.iflytek.aicc.flow.common.enums.SpeechManagerEnums.SpeechPublishOperationEnum.STATUS_PUBLISH;

/**
 * 描述：话术对话流管理实现类
 *
 * <AUTHOR>
 * @date 2023/9/27 10:28
 */
@Service
@Slf4j
public class SpeechFlowManagerServiceImpl implements SpeechFlowManagerService {

    @Value("${flow.jsonfile.filePath:flow}")
    private String filePath;

    @Value("${flow.jsonfile.temp.filePath:temp}")
    private String tempFilePath;

    @Value("${s3.location:/s3-file-location}")
    private String location;
    @Value("${s3.path.package:}")
    private String pathPackage;

    @Value("${viewSelfRoleId:1666684222832640}")
    private String viewSelfRoleId;

    @Value("${speechManagerRoleId:1669647263727616}")
    private String speechManagerRoleId;

    @Value("${dynamicSpeechPublishUrl:http://outbound-operations-manager/v1/pt/speech-module/syncSpeech}")
    private String dynamicSpeechPublishUrl;
    /**
     * 交付管理员id
     */
    @Value("${deliveryRoleId:1858199649935360}")
    private String deliveryRoleId;

    @Value("${adminUserId:100001}")
    private String adminUserId;
    /**
     * 完整回答
     */
    public static final String COMPLETED_ANSWER = "COMPLETED_ANSWER";
    /**
     * 正常回答
     */
    public static final String NORMAL_ANSWER = "NORMAL_ANSWER";
    /**
     * 完整回答节点名称集合
     */
    public static final List<String> NODENAMELIST = Arrays.asList("号码错误", "居民已死亡");
    //匹配单引号之前的内容
    private static final Pattern CONDITION_NAME_PATTERN = Pattern.compile("'([^']*)'");
    // 定义正则表达式模式
    private static final Pattern LINKNAME_EXPLAIN_PATTERN = Pattern.compile("^(.*?)(：|:)(.*)$", Pattern.MULTILINE);
    // 定义正则表达式模式 匹配 #{}
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("#\\{(.*?)\\}");

    /**
     * 排除的语义接口编码
     */
    public static final List<String> EXCLUDE_INTERFACE_CODE = Arrays.asList(
            "relatives_iris",
            "relativesNew",
            "relatives_iris2",
            "relativesWY_iris",
            "selfIntroduction_iris1",
            "selfIntroductionNew",
            "selfIntroduction_iris2",
            "selfIntroductionWY_iris",
            "selfIntroduction_iris",
            "insteadReply_iris",
            "relatives_iris",
            "relativesNew",
            "relatives_iris2",
            "relativesWY_iris");

    public static final String EXCLUDE_INTERFACE_CODE_DICT = "excludeInterfaceCode";

    private final FlowConfig flowConfig;
    private final FileUploadUtils fileUploadUtils;
    private final SpeechBaseRepository speechBaseRepository;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final SpeechFlowConfigSaveService speechFlowConfigSaveService;
    private final UidService uidService;
    private final PublishRecordRepository publishRecordRepository;
    private final UserOrgQueryApi userOrgQueryApi;
    private final RedisCacheService redisCacheService;
    private final RedissonService redissonService;
    private final ZipFileUtils zipFileUtils;
    private final S3Properties s3Properties;
    private final SpeechFragmentMapper speechFragmentMapper;
    private final SpeechVariableService speechVariableService;
    private final NlpInterfaceService nlpInterfaceService;
    private final AiccFlowService aiccFlowService;
    private final SpeechBaseFragmentRelRepository speechBaseFragmentRelRepository;
    private final SpeechFragmentRepository speechFragmentRepository;
    private final FileDownloadUtils fileDownloadUtils;
    private final FlowCacheManager flowCacheManager;
    private final SpeechKnowledgeRepository speechKnowledgeRepository;
    private final SpeechConfigRepository speechConfigRepository;
    private final SystemDictService systemDictService;
    private final LlmServiceFactory llmServiceFactory;
    private final LlmTemplateRepository llmTemplateRepository;

    private final PlatCodeUserRepository platCodeUserRepository;

    private final SpeechSceneConfigRepository speechSceneConfigRepository;

    public SpeechFlowManagerServiceImpl(FileUploadUtils fileUploadUtils, SpeechBaseRepository speechBaseRepository, FlowConfig flowConfig,
                                        ApplicationEventPublisher applicationEventPublisher,
                                        SpeechFlowConfigSaveService speechFlowConfigSaveService, UidService uidService,
                                        PublishRecordRepository publishRecordRepository, UserOrgQueryApi userOrgQueryApi, RedisCacheService redisCacheService,
                                        ZipFileUtils zipFileUtils, S3Properties s3Properties, RedissonService redissonService,
                                        SpeechFragmentMapper speechFragmentMapper, SpeechVariableService speechVariableService,
                                        NlpInterfaceService nlpInterfaceService, AiccFlowService aiccFlowService,
                                        SpeechBaseFragmentRelRepository speechBaseFragmentRelRepository, SpeechFragmentRepository speechFragmentRepository,
                                        FileDownloadUtils fileDownloadUtils, FlowCacheManager flowCacheManager,
                                        SpeechKnowledgeRepository speechKnowledgeRepository,
                                        SpeechConfigRepository speechConfigRepository, SystemDictService systemDictService,
                                        LlmServiceFactory llmServiceFactory, LlmTemplateRepository llmTemplateRepository,
                                        PlatCodeUserRepository platCodeUserRepository, SpeechSceneConfigRepository speechSceneConfigRepository) {
        this.fileUploadUtils = fileUploadUtils;
        this.speechBaseRepository = speechBaseRepository;
        this.flowConfig = flowConfig;
        this.applicationEventPublisher = applicationEventPublisher;
        this.speechFlowConfigSaveService = speechFlowConfigSaveService;
        this.uidService = uidService;
        this.publishRecordRepository = publishRecordRepository;
        this.userOrgQueryApi = userOrgQueryApi;
        this.redisCacheService = redisCacheService;
        this.zipFileUtils = zipFileUtils;
        this.s3Properties = s3Properties;
        this.redissonService = redissonService;
        this.speechFragmentMapper = speechFragmentMapper;
        this.speechVariableService = speechVariableService;
        this.nlpInterfaceService = nlpInterfaceService;
        this.aiccFlowService = aiccFlowService;
        this.speechBaseFragmentRelRepository = speechBaseFragmentRelRepository;
        this.speechFragmentRepository = speechFragmentRepository;
        this.fileDownloadUtils = fileDownloadUtils;
        this.flowCacheManager = flowCacheManager;
        this.speechKnowledgeRepository = speechKnowledgeRepository;
        this.speechConfigRepository = speechConfigRepository;
        this.systemDictService = systemDictService;
        this.llmServiceFactory = llmServiceFactory;
        this.llmTemplateRepository = llmTemplateRepository;
        this.platCodeUserRepository = platCodeUserRepository;
        this.speechSceneConfigRepository = speechSceneConfigRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveSpeechFlow(SpeechFlowDto speechFlowDto) {
        // todo 保持事务性，先更新数据库再上传图片，可能出现数据库和文件不一致情况
        //前置逻辑校验
        SpeechBaseEntity speechBaseEntity = getSpeechBaseById(speechFlowDto.getSpeechId(), null);
        if (null == speechBaseEntity) {
            throw new MedicalBusinessException("话术不存在");
        }
        // 先查询如果已经存在则更新
        String flowFilePath = speechBaseEntity.getFlowFilePath();
        String flowJson = AesEncryptUtil.desEncrypt(speechFlowDto.getFlowJson());
        FlowBaseConfig flowBaseConfig = JacksonUtils.readValue(flowJson, FlowBaseConfig.class);
        if (null == flowBaseConfig) {
            throw new MedicalBusinessException("话术流程图配置有误");
        }
        //处理正常/完整回答
        flowJson = setAnswerTag(flowJson, flowBaseConfig);
        String fileUrl = uploadFlowJson(null, speechFlowDto.getSpeechId(), flowJson, flowFilePath);
        speechBaseEntity.setFlowFilePath(fileUrl);

        String tempFlowJson = speechFlowDto.getTempFlowJson();
        if (!StringUtils.isEmpty(tempFlowJson)) {
            String tempFlowFilePath = speechBaseEntity.getTempFlowFilePath();
            tempFlowJson = AesEncryptUtil.desEncrypt(tempFlowJson);

            tempFlowFilePath = uploadFlowJson(tempFilePath, speechFlowDto.getSpeechId(), tempFlowJson, tempFlowFilePath);
            speechBaseEntity.setTempFlowFilePath(tempFlowFilePath);
        } else {
            speechBaseEntity.setTempFlowFilePath(null);
        }
        speechBaseEntity.setUpdatedBy(speechFlowDto.getLoginUserId());
        speechBaseEntity.setUpdatedTime(new Date());
        speechBaseEntity.setStatus(SpeechManagerEnums.SpeechPublishStatusEnum.STATUS_TO_RELEASED.getCode());
        String speechFragmentId = getSpeechFragmentId(flowBaseConfig);
        speechBaseEntity.setSpeechFragmentId(speechFragmentId);
        speechBaseEntity.setDeliveryLlmSwitch(speechFlowDto.getDeliveryLlmSwitch());
        speechBaseRepository.updateSpeech(speechBaseEntity);
        if (SpeechManagerEnums.SpeechTypeEnum.STATUS_DYNAMIC.getCode().equals(speechBaseEntity.getSpeechType())) {
            List<SpeechBaseFragmentRelEntity> speechBaseFragmentRelEntity = getSpeechBaseFragment(speechBaseEntity);
            speechBaseFragmentRelRepository.deleteAndInsert(speechBaseFragmentRelEntity);
        }
        return speechFlowDto.getSpeechId();
    }

    private String setAnswerTag(String flowJson, FlowBaseConfig flowBaseConfig) {
        JSONObject flowBaseJsonObject = JSON.parseObject(flowJson, JSONObject.class);
        JSONArray nodeArray = flowBaseJsonObject.getJSONArray("node");
        Map<String, Node> nodeMap = flowCacheManager.getNodeMap(flowBaseConfig);
        //排除的节点集合
        Set<String> excludeNodeSet = getExcludeNodeSet(nodeMap);
        for (int i = 0; i < nodeArray.size(); i++) {
            JSONObject node = nodeArray.getJSONObject(i);
            //不为空，取页面的配置
            JSONArray tagsArray = node.getJSONArray("tags");
            if (!tagsArray.isEmpty()) {
                continue;
            }
            String id = node.getString("id");
            if (excludeNodeSet.contains(id)) {
                tagsArray.clear();
                continue;
            }
            FlowLabelEnum flowLabelEnum = FlowLabelEnum.NORMAL_ANSWER;
            Integer type = node.getInteger("type");
            if (type == 5) {
                flowLabelEnum = FlowLabelEnum.COMPLETED_ANSWER;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", flowLabelEnum.getCode());
            jsonObject.put("name", flowLabelEnum.getMessage());
            tagsArray.add(jsonObject);
        }
        return JacksonUtils.writeValueAsString(flowBaseJsonObject);
    }

    @Override
    public Set<String> getExcludeNodeSet(Map<String, Node> nodeMap) {
        Set<String> excludeInterfaceCode = getExcludeInterfaceCode();

        Set<String> excludeNodeSet = new HashSet<>();
        for (Node node : nodeMap.values()) {
            NodeSetting setting = node.getSetting();
            if (Objects.isNull(setting)) {
                excludeNodeSet.add(node.getId());
                continue;
            }
            NodeNlp nlp = setting.getNlp();
            if (Objects.isNull(nlp)) {
                excludeNodeSet.add(node.getId());
                continue;
            }
            String code = nlp.getCode();
            if (excludeInterfaceCode.contains(code)) {
                excludeNodeSet.add(node.getId());
                List<ConditionLink> linkers = node.getLinkers();
                if (!CollectionUtils.isEmpty(linkers)) {
                    for (ConditionLink linker : linkers) {
                        String toNodeId = linker.getToNode();
                        excludeNodeSet.add(toNodeId);
                        Integer id = Optional.ofNullable(linker.getCommonEnd()).map(CommonEnd::getId).orElse(0);
                        excludeNodeSet.add(id.toString());
                    }
                }
                String toNodeId = Optional.ofNullable(node.getMultipleFailed()).map(PlayNode.MultipleFailed::getToNodeId).orElse("");
                excludeNodeSet.add(toNodeId);
            }
        }
        return excludeNodeSet;
    }

    @Override
    public Boolean checkDeliveryRole(String loginUserId) {
        return deliveryRole(loginUserId);
    }

    @Override
    public Set<String> getExcludeInterfaceCode() {
        Set<String> excludeInterfaceCode = new HashSet<>(EXCLUDE_INTERFACE_CODE);
        //增加字段配置语义接口
        Optional.ofNullable(systemDictService.getDictByValue(EXCLUDE_INTERFACE_CODE_DICT))
                .map(DictPackageVO::getChild)
                .filter(children -> !CollectionUtils.isEmpty(children))
                .ifPresent(children -> children.stream()
                        .map(DictPackageVO::getDictValue)
                        .forEach(excludeInterfaceCode::add));
        return excludeInterfaceCode;
    }

    /**
     * 获取话术片段与话术对应关系
     *
     * @param speechBaseEntity
     * @return
     */
    private List<SpeechBaseFragmentRelEntity> getSpeechBaseFragment(SpeechBaseEntity speechBaseEntity) {
        //获取话术片段id
        String speechFragmentId = speechBaseEntity.getSpeechFragmentId();
        if (StringUtils.isEmpty(speechFragmentId)) {
            return null;
        }
        List<SpeechBaseFragmentRelEntity> speechBaseFragmentRelEntityList = new ArrayList<>();
        for (String fragmentId : speechFragmentId.split(",")) {
            SpeechBaseFragmentRelEntity speechBaseFragmentRelEntity = new SpeechBaseFragmentRelEntity();
            speechBaseFragmentRelEntity.setSpeechFragmentId(Long.valueOf(fragmentId));
            speechBaseFragmentRelEntity.setRelState(SpeechFragmentRelEnums.NORMAL.getCode());
            speechBaseFragmentRelEntity.setSpeechId(speechBaseEntity.getId());
            speechBaseFragmentRelEntity.setCreatedBy(speechBaseEntity.getCreatedBy());
            speechBaseFragmentRelEntity.setCreatedTime(new Date());
            speechBaseFragmentRelEntity.setUpdatedBy(speechBaseEntity.getUpdatedBy());
            speechBaseFragmentRelEntity.setUpdatedTime(new Date());
            speechBaseFragmentRelEntityList.add(speechBaseFragmentRelEntity);
        }
        return speechBaseFragmentRelEntityList;
    }

    @Override
    public String uploadFlowJson(String serverFilePath, Long speechId, String flowJson, String flowFilePath) {
        String savePath;
        serverFilePath = StringUtils.isEmpty(serverFilePath) ? filePath : serverFilePath;
        if (StringUtils.isEmpty(flowFilePath)) {
            //文件上传
            savePath = serverFilePath + FLOW_FILEPATH_SPLIT + speechId + FLOW_FILENAME_SPLIT + System.currentTimeMillis() + FLOW_FILE_PREFIX;
        } else {
            if (flowFilePath.lastIndexOf(serverFilePath + FLOW_FILEPATH_SPLIT) != -1) {
                savePath = flowFilePath.substring(flowFilePath.lastIndexOf(serverFilePath + FLOW_FILEPATH_SPLIT));
            } else {
                savePath = flowFilePath;
            }
        }
        return uploadFlowFile(flowJson, savePath);
    }

    @Override
    public boolean lockOperateSpeechById(SpeechLockOperateParam speecLockOperateParam, String loginUserId) {
        SpeechBaseEntity speechBaseEntity = getSpeechBaseById(speecLockOperateParam.getSpeechId(), null);
        if (null == speechBaseEntity) {
            throw new MedicalBusinessException("话术不存在");
        }
        //todo 锁定逻辑待确认
        return false;
    }

    @Override
    public List<SpeechBaseEntity> getSpeechBaseByIds(Set<Long> ids) {
        return speechBaseRepository.getSpeechBaseByIds(ids);
    }

    @Override
    public String uploadFlowFile(String flowJson, String savePath) {
        ByteArrayInputStream inputStream;
        String fileUrl;
        try {
            inputStream = new ByteArrayInputStream(flowJson.getBytes(StandardCharsets.UTF_8));
            savePath = savePath.startsWith(FLOW_FILEPATH_SPLIT) ? savePath.substring(1) : savePath;
            fileUrl = fileUploadUtils.uploadFile(inputStream, savePath);
            if (StringUtils.isEmpty(fileUrl)) {
                throw new MedicalBusinessException("对话流文件上传失败");
            }
            fileUrl = fileUrl.replace(location, "");
            log.info("对话处理成功，文件地址：{}", fileUrl);
        } catch (Exception e) {
            log.error("对话处理失败", e);
            throw new MedicalBusinessException("对话处理失败");
        }
        return fileUrl;
    }

    @Override
    public SpeechFlowDetailDto speechFlowDetail(String loginUserId, Long speechId, boolean isPublish) {
        //医生ID做权限校验
        SpeechBaseEntity speechBaseEntity = getSpeechBaseById(speechId, null);
        if (speechBaseEntity == null) {
            throw new MedicalBusinessException("话术不存在");
        }
        if (StringUtils.isNotBlank(loginUserId) && isViewSelfRole(loginUserId)) {
            if (!loginUserId.equals(speechBaseEntity.getCreatedBy())) {
                throw new MedicalBusinessException("对不起，您无查看该话术权限");
            }
        }

        String flowFilePath;
        if (isPublish) {
            flowFilePath = speechBaseEntity.getPublishFilePath();
        } else {
            flowFilePath = speechBaseEntity.getFlowFilePath();
        }
        SpeechFlowDetailDto speechFlowDetailDto = new SpeechFlowDetailDto();
        if (!StringUtils.isEmpty(flowFilePath)) {
            JSONObject flowBaseConfig = buildCommonEnd(flowFilePath);
            speechFlowDetailDto.setFlowJson(AesEncryptUtil.encrypt(JSON.toJSONString(flowBaseConfig)));
        }
        String tempFlowFilePath = speechBaseEntity.getTempFlowFilePath();
        if (!StringUtils.isEmpty(tempFlowFilePath)) {
            String encrypt = AesEncryptUtil.encrypt(downJsonFile(tempFlowFilePath));
            speechFlowDetailDto.setTempFlowJson(encrypt);
        }
        speechFlowDetailDto.setSpeechId(speechId);
        speechFlowDetailDto.setSpeechName(speechBaseEntity.getSpeechName());
        speechFlowDetailDto.setSpeechDesc(speechBaseEntity.getSpeechIntro());
        speechFlowDetailDto.setSpeechType(speechBaseEntity.getSpeechType());
        Long business = StringUtils.isEmpty(speechBaseEntity.getBusinessDirection()) ? null : Long.valueOf(speechBaseEntity.getBusinessDirection());
        speechFlowDetailDto.setBusinessDirection(business);
        speechFlowDetailDto.setSpeechScene(speechBaseEntity.getSpeechScene());
        speechFlowDetailDto.setPlatCode(speechBaseEntity.getPlatCode());
        speechFlowDetailDto.setRange(speechBaseEntity.getRange());
        speechFlowDetailDto.setDeliveryLlmSwitch(speechBaseEntity.getDeliveryLlmSwitch());
        return speechFlowDetailDto;
    }

    @NotNull
    private JSONObject buildCommonEnd(String flowFilePath) {
        JSONObject flowBaseConfig = JSON.parseObject(downJsonFile(flowFilePath), JSONObject.class);

        if (null == flowBaseConfig) {
            throw new MedicalBusinessException("话术流程图配置有误");
        }
        DictPackageVO dictPackageVO = systemDictService.getDictByValue(FlowCacheManager.COMMON_NODE);
        if (Objects.isNull(dictPackageVO)) {
            return flowBaseConfig;
        }
        Map<Integer, String> commonEndMap = dictPackageVO.getChild().stream().collect(Collectors.toMap(DictPackageVO::getId, DictPackageVO::getDictName));
        JSONArray nodeArray = flowBaseConfig.getJSONArray("node");
        if (!CollectionUtils.isEmpty(nodeArray)) {
            for (int i = 0; i < nodeArray.size(); i++) {
                JSONObject node = nodeArray.getJSONObject(i);
                //处理多次未识别通用结束节点
                handleMultipleFailed(node, commonEndMap);
                //处理跳转结束节点
                handleLinkers(node, commonEndMap);
            }
        }
        return flowBaseConfig;
    }

    private static void handleLinkers(JSONObject node, Map<Integer, String> commonEndMap) {
        JSONArray linkers = node.getJSONArray("linkers");
        if (CollectionUtils.isEmpty(linkers)) {
            return;
        }
        for (int i = 0; i < linkers.size(); i++) {
            JSONObject linker = linkers.getJSONObject(i);
            String forward = linker.getString("forward");
            // 数字类型代表通用结束节点（前端生成的id不是数字）
            if (StringUtils.isEmpty(forward) || !FlowCacheManager.isNumeric(forward)) {
                continue;
            }
            JSONObject commonEnd = linker.getJSONObject("commonEnd");
            if (Objects.isNull(commonEnd)) {
                continue;
            }
            commonEnd.put("id", forward);
            commonEnd.put("name", commonEndMap.get(Integer.valueOf(forward)));
        }
    }

    private static void handleMultipleFailed(JSONObject node, Map<Integer, String> commonEndMap) {
        JSONObject multipleFailed = node.getJSONObject("multiple_failed");
        if (Objects.nonNull(multipleFailed) && StringUtils.isNotEmpty(multipleFailed.getString("to"))) {
            String nodeId = multipleFailed.getString("to");
            // 数字类型代表通用结束节点（前端生成的id不是数字）
            if (FlowCacheManager.isNumeric(nodeId)) {
                JSONObject commonEnd = multipleFailed.getJSONObject("commonEnd");
                if (Objects.nonNull(commonEnd)) {
                    commonEnd.put("id", nodeId);
                    commonEnd.put("name", commonEndMap.get(Integer.valueOf(nodeId)));
                }
            }
        }
    }

    /**
     * 下载json文件
     *
     * @param filePath 文件地址
     * @return 返回json
     */
    @Override
    public String downJsonFile(String filePath) {
        try {
            byte[] data = fileDownloadUtils.downJsonFile(filePath);
            return new String(data, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("对话流文件读取异常", e);
            throw new MedicalBusinessException("对话流文件读取异常");
        }
    }

    @Override
    public SpeechBaseEntity getSpeechBaseById(Long id, Integer publishStatus) {
        return speechBaseRepository.getSpeechBaseById(id, publishStatus);
    }

    @Override
    public SpeechBaseEntity getSpeechBaseById(Long id) {
        return speechBaseRepository.getSpeechBaseById(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String releaseFlow(Long speechId, String loginUserId, HttpServletRequest request) {
        //医生ID做权限校验
        SpeechBaseEntity speechBaseEntity = getSpeechBaseById(speechId, SpeechManagerEnums.SpeechPublishStatusEnum.STATUS_TO_RELEASED.getCode());
        if (speechBaseEntity == null) {
            throw new MedicalBusinessException("话术不存在或非待发布状态");
        }
        String flowFilePath = speechBaseEntity.getFlowFilePath();
        if (StringUtils.isEmpty(flowFilePath)) {
            throw new MedicalBusinessException("话术流程不存在");
        }
        SpeechReleaseDto speechReleaseDto = releaseFlow(speechBaseEntity, loginUserId, request);
        //话术发布到运管
        publishToOperationManagement(speechBaseEntity, speechReleaseDto.getFlowBaseConfig());
        //话术发布到另一个能力平台
        publishToOtherPlatform(speechBaseEntity);
        return speechReleaseDto.getMd5Value();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpeechReleaseDto releaseFlow(SpeechBaseEntity speechBaseEntity, String loginUserId, HttpServletRequest request) {
        String data = downJsonFile(speechBaseEntity.getFlowFilePath());
        Long speechId = speechBaseEntity.getId();
        String md5Value = IdUtil.fastSimpleUUID();
        String savePath = filePath + FLOW_FILEPATH_SPLIT + speechId + FLOW_FILEPATH_SPLIT + speechId + FLOW_FILENAME_SPLIT + md5Value + FLOW_FILE_PREFIX;
        String releaseFileUrl = uploadFlowFile(data, savePath);
        speechBaseEntity.setPublishFilePath(releaseFileUrl);
        speechBaseEntity.setMd5Value(md5Value);
        speechBaseEntity.setStatus(SpeechManagerEnums.SpeechPublishStatusEnum.STATUS_RELEASED.getCode());
        speechBaseEntity.setUpdatedBy(loginUserId);
        speechBaseEntity.setUpdatedTime(new Date());
        speechBaseEntity.setPublishBy(loginUserId);
        speechBaseEntity.setPublishTime(new Date());
        // 增加变量保存
        FlowBaseConfig flowBaseConfig = JacksonUtils.readValue(data, FlowBaseConfig.class);
        // 保存变量
        String variableJoinStr = Optional.ofNullable(flowBaseConfig).map(FlowBaseConfig::getVariableList)
                .map(tempList -> tempList.stream().filter(baseVariable -> null == baseVariable.getInternal() || !baseVariable.getInternal())
                        .map(BaseVariable::getName)
                        .collect(Collectors.joining(","))).orElse(null);
        speechBaseEntity.setVariables(variableJoinStr);
        //获取话术片段id
        String speechFragmentId = getSpeechFragmentId(flowBaseConfig);
        //获取话术类型和大模型阶段
        setSpeechModelType(speechBaseEntity, flowBaseConfig);
        speechBaseEntity.setSpeechFragmentId(speechFragmentId);
        //保存操作记录
        publishOperationRecordEvent(Collections.singletonList(speechBaseEntity), loginUserId, request);
        speechBaseRepository.updateSpeech(speechBaseEntity);
        addPublishRecord(speechBaseEntity, loginUserId);
        //更新话术基础表 话术节点表 话术变量表 话术内知识库 等
        speechFlowConfigSaveService.saveFlowNodeConfig(flowBaseConfig, loginUserId, speechBaseEntity.getId());
        applicationEventPublisher.publishEvent(new FlowCacheEvent(this, speechId, data, STATUS_PUBLISH.getCode()));
        return SpeechReleaseDto.builder()
                .md5Value(md5Value)
                .flowBaseConfig(flowBaseConfig)
                .build();
    }

    private void setSpeechModelType(SpeechBaseEntity speechBaseEntity, FlowBaseConfig flowBaseConfig) {
        if (flowBaseConfig == null || CollectionUtils.isEmpty(flowBaseConfig.getNode())) {
            return;
        }
        //若设置了自由调度，则为大模型+阶段二
        if (flowBaseConfig.isFreeSelectFlag()) {
            speechBaseEntity.setSpeechMode(Constants.BIG_MODEL);
            speechBaseEntity.setModelStage(Constants.STAGE_TWO);
            return;
        }
        //非自由调度，判断是否存在节点开启了大模型
        for (Node node : flowBaseConfig.getNode()) {
            NodeSetting.LlmParam llmParam = node.getSetting().getLlmParam();
            if (llmParam == null) {
                continue;
            }
            if (llmParam.getEnable() != null && llmParam.getEnable()) {
                speechBaseEntity.setSpeechMode(Constants.BIG_MODEL);
                speechBaseEntity.setModelStage(Constants.STAGE_ONE);
                return;
            } else {
                speechBaseEntity.setSpeechMode(Constants.TRADITION);
                speechBaseEntity.setModelStage(null);
            }
        }
    }

    @Override
    public void publishToOtherPlatform(SpeechBaseEntity speechBaseEntity) {
        Integer speechType = speechBaseEntity.getSpeechType();
        Boolean syncSwitch = aiccFlowService.syncSwitch();
        if (!syncSwitch) {
            log.info("SpeechFlowManagerServiceImpl[publishToOtherPlatform] 同步开关未开启:{}", JSON.toJSONString(speechBaseEntity));
            return;
        }
        SpeechFlowSyncParam speechFlowSyncParam = new SpeechFlowSyncParam();
        speechFlowSyncParam.setUpdateBy(speechBaseEntity.getUpdatedBy());
        List<SpeechBaseFragmentRelEntity> speechBaseFragmentRelEntityList = null;
        if (SpeechManagerEnums.SpeechTypeEnum.STATUS_DYNAMIC.getCode().equals(speechType)) {
            speechBaseFragmentRelEntityList = speechBaseFragmentRelRepository.listBySpeechId(speechBaseEntity.getId());
        }
        Map<String, String> uapUserMap = getUapUserMap(Collections.singletonList(speechBaseEntity));
        String flowJson = downJsonFile(speechBaseEntity.getFlowFilePath());
        SpeechManagerExportEntity exportEntity = SpeechManagerExportEntity.builder().id(speechBaseEntity.getId())
                .speechName(speechBaseEntity.getSpeechName()).speechIntro(speechBaseEntity.getSpeechIntro())
                .status(SpeechManagerEnums.SpeechPublishStatusEnum.getByCode(speechBaseEntity.getStatus()).getDesc())
                .createdBy(uapUserMap.get(speechBaseEntity.getCreatedBy()))
                .createdTime(new DateTime(speechBaseEntity.getCreatedTime()).toString(CommonUtil.DAY_DATE_FORMAT))
                .flowJson(AesEncryptUtil.encrypt(flowJson))
                .speechType(speechType)
                .deleted(speechBaseEntity.getDeleted())
                .speechBaseFragmentRelEntityList(speechBaseFragmentRelEntityList)
                .speechMode(speechBaseEntity.getSpeechMode())
                .modelStage(speechBaseEntity.getModelStage())
                .build();
        speechFlowSyncParam.setSpeechManagerExportEntity(exportEntity);
        aiccFlowService.publishToOtherPlatform(speechFlowSyncParam);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncFlow(SpeechFlowSyncParam speechFlowSyncParam, HttpServletRequest request) {
        SpeechManagerExportEntity entity = speechFlowSyncParam.getSpeechManagerExportEntity();
        String loginUserId = speechFlowSyncParam.getUpdateBy();
        try {
            Integer deleted = entity.getDeleted();
            if (Objects.nonNull(deleted) && deleted == 1) {
                Long speechId = entity.getId();
                speechBaseRepository.deleteSpeech(loginUserId, speechId);
                applicationEventPublisher.publishEvent(new FlowCacheEvent(this, speechId, null, STATUS_DELETE.getCode()));
                return Boolean.TRUE.toString();
            }
            List<File> fileList = getFileList(entity);
            //设置状态不需要同步，此接口就是接受别的能力平台同步过来的话术，否则陷入循环当中
            request.setAttribute(Constants.NO_NEED_SYNC, true);
            saveOrUpdate(Collections.singletonList(fileList), loginUserId, request);
        } catch (IOException e) {
            log.error("话术同步失败", e);
            throw new MedicalBusinessException("话术同步失败");
        }
        return Boolean.TRUE.toString();
    }

    @Override
    public List<SpeechFragmentSimpleVO> getChangeFragmentInfo(Long id) {
        List<SpeechBaseFragmentRelEntity> speechBaseFragmentRelEntityList = speechBaseFragmentRelRepository.listBySpeechId(id);
        if (CollectionUtils.isEmpty(speechBaseFragmentRelEntityList)) {
            return null;
        }
        List<Long> fragmentIds = speechBaseFragmentRelEntityList.stream().filter(fragment ->
                        fragment.getRelState().equals(1)).map(SpeechBaseFragmentRelEntity::getSpeechFragmentId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fragmentIds)) {
            return null;
        }
        List<SpeechFragmentEntity> speechFragmentEntities = speechFragmentRepository.listById(fragmentIds);
        if (CollectionUtils.isEmpty(speechFragmentEntities)) {
            return null;
        }
        List<Long> nlpNodeIdList = getNodeIdList(speechFragmentEntities);

        Map<Long, NlpNodeResponse> nlpNodeidMap = getNlpNodeIdMap(nlpNodeIdList);

        List<SpeechFragmentSimpleVO> simpleVOList = new ArrayList<>();
        for (SpeechFragmentEntity speechFragmentEntity : speechFragmentEntities) {
            String publishFilePath = speechFragmentEntity.getPublishFilePath();
            String json = this.downJsonFile(publishFilePath);
            SpeechFragmentSimpleVO speechFragmentSimpleVO = new SpeechFragmentSimpleVO();
            speechFragmentSimpleVO.setId(speechFragmentEntity.getId());
            speechFragmentSimpleVO.setName(speechFragmentEntity.getName());
            speechFragmentSimpleVO.setFlowJson(AesEncryptUtil.encrypt(json));
            speechFragmentSimpleVO.setMd5Value(speechFragmentEntity.getMd5Value());

            FlowBaseConfig flowBaseConfig = JacksonUtils.readValue(json, FlowBaseConfig.class);
            if (flowBaseConfig != null) {
                List<Node> nodeList = flowBaseConfig.getNode().stream()
                        .filter(node -> Objects.equals(PlayNodeEnum.NodeTypeEnum.PLAY_NODE.getCode(), node.getType())).collect(Collectors.toList());
                Map<String, String> nlpNodeNameAndCodeMap = new HashMap<>();
                for (Node node : nodeList) {
                    Long nlpNodeId = node.getNlpNodeId();
                    NlpNodeResponse nlpNodeResponse = nlpNodeidMap.get(nlpNodeId);
                    if (Objects.isNull(nlpNodeResponse)) {
                        continue;
                    }
                    nlpNodeNameAndCodeMap.put(nlpNodeResponse.getNodeName(), nlpNodeResponse.getNodeCode());
                }
                speechFragmentSimpleVO.setNlpNodeNameAndCodeMap(nlpNodeNameAndCodeMap);
            }

            simpleVOList.add(speechFragmentSimpleVO);
        }
        return simpleVOList;
    }

    @NotNull
    private Map<Long, NlpNodeResponse> getNlpNodeIdMap(List<Long> nlpNodeIdList) {
        Map<Long, NlpNodeResponse> nlpNodeidMap = new HashMap<>(16);
        if (!org.springframework.util.CollectionUtils.isEmpty(nlpNodeIdList)) {
            NlpNodeListParam nlpNodeListParam = new NlpNodeListParam();
            nlpNodeListParam.setPageSize(999);
            nlpNodeListParam.setPageIndex(1);
            nlpNodeListParam.setIds(nlpNodeIdList);
            PageList<NlpNodeResponse> responsePageList = nlpInterfaceService.queryNlpNodeList(nlpNodeListParam);
            if (!org.springframework.util.CollectionUtils.isEmpty(responsePageList.getData())) {
                nlpNodeidMap = responsePageList.getData().stream().collect(Collectors.toMap(NlpNodeResponse::getId, Function.identity()));
            }
        }
        return nlpNodeidMap;
    }

    @NotNull
    private static List<Long> getNodeIdList(List<SpeechFragmentEntity> speechFragmentEntities) {
        List<Long> nlpNodeIdList = new ArrayList<>();
        for (SpeechFragmentEntity speechFragment : speechFragmentEntities) {
            String nlpNodeId = speechFragment.getNlpNodeId();
            if (StringUtils.isEmpty(nlpNodeId)) {
                continue;
            }
            nlpNodeIdList.addAll(JSON.parseArray(nlpNodeId, Long.class));
        }
        return nlpNodeIdList;
    }

    @Override
    @Async
    public void exportMatchNode(SpeechManagerListBO listBO, long taskId) {
        ExportResultBO exportResultBO = new ExportResultBO();
        exportResultBO.setTaskId(taskId);
        SpeechManagerListDO listDo = SpeechManagerConvertor.toDo(listBO);
        if (!isViewSelfRole(listBO.getLoginUserId())) {
            listDo.setCreator(null);
        }
        PageList<SpeechBaseEntity> speechBaseEntityPageList = speechBaseRepository.getManagerList(listDo, null);
        if (CollectionUtils.isEmpty(speechBaseEntityPageList.getData())) {
            exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            exportResultBO.setMessage("未查询到话术信息");
            saveExportResult(exportResultBO);
            return;
        }
        try {
            List<MatchNodeExportDto> nodeList = new ArrayList<>();
            for (SpeechBaseEntity speechBaseEntity : speechBaseEntityPageList.getData()) {
                String flowFilePath = speechBaseEntity.getFlowFilePath();
                //下载话术流程
                byte[] data = fileDownloadUtils.downJsonFile(flowFilePath);
                String flowText = new String(data, StandardCharsets.UTF_8);
                FlowBaseConfig flowBaseConfig = JacksonUtils.readValue(flowText, FlowBaseConfig.class);
                //获取节点id和对应的放音内容
                Map<String, String> nodeMap = flowBaseConfig.getNode().stream().filter(node -> node instanceof PlayRespondNode)
                        .filter(node -> CollectionUtils.isNotEmpty(node.getPromptList()))
                        .collect(Collectors.toMap(Node::getId, node -> node.getPromptList().get(0).getText(), (a, b) -> b));
                for (Node node : flowBaseConfig.getNode()) {
                    if (node instanceof AnyMatchNode) {
                        continue;
                    }
                    if (node instanceof PlayRespondNode) {
                        if (CollectionUtils.isEmpty(node.getLinkers()) || CollectionUtils.isEmpty(node.getPromptList())) {
                            continue;
                        }
                        //todo node
                        // 默认衔接语言 node.getCommonLinkerNodePrompt().getText();
                        // node.getMultipleFailed().getPrompts().get(0).getText();
                        String text = node.getPromptList().get(0).getText();
                        for (ConditionLink o : node.getLinkers()) {
                            String nextPlayText = getNextPlayText(flowBaseConfig, nodeMap, o);
                            nodeList.add(new MatchNodeExportDto(speechBaseEntity.getId().toString(), text, node.getName(), o.getName(),
                                    o.getPrompts().get(0).getText(),
                                    o.getNameExplain(), o.getCondition(), nextPlayText));
                        }
                    }
                }
            }
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream, MatchNodeExportDto.class)
                    .registerWriteHandler(new CommonAbstractVerticalCellStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("话术出口节点导出").build();
            excelWriter.write(nodeList, writeSheet);
            excelWriter.finish();
            InputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            String date = DateTime.now().toString(DateUtil.PATTERN_DEFAULT);
            String uploadPath = "file/" + "话术出口节点导出_" + date + ".xlsx";
            String url = fileUploadUtils.uploadByInputStream(byteArrayInputStream, uploadPath, ContainerParamEnum.ZHYL_AICC_UP_DOWN_FILE);
            url = fileUploadUtils.getCompleteUrl(url);
            if (StringUtils.isEmpty(url)) {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            } else {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.SUCCESS.getCode());
                exportResultBO.setUrl(url);
            }
        } catch (Exception e) {
            log.error("话术出口节点导出异常", e);
            exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            exportResultBO.setMessage(e.getMessage());
        }
        saveExportResult(exportResultBO);
    }

    private String getNextPlayText(FlowBaseConfig flowBaseConfig, Map<String, String> nodeMap, ConditionLink o) {
        Node skipNode = flowBaseConfig.getNode().stream().filter(n -> n.getId().equals(o.getToNode())).findFirst().orElse(null);
        if (skipNode instanceof AnyMatchNode) {
            return nodeMap.get(((AnyMatchNode) skipNode).getSkipCondition().getTo());
        }
        if (StringUtils.isNotBlank(nodeMap.get(o.getToNode()))) {
            return nodeMap.get(o.getToNode());
        }
        if (o.getCommonEnd() != null && StringUtils.isNotBlank(o.getCommonEnd().getNodeText())) {
            return o.getCommonEnd().getNodeText();
        }
        return "";
    }

    @Override
    @Async
    public void exportAnalysisConfig(SpeechManagerListBO listBO, long taskId) {
        ExportResultBO exportResultBO = new ExportResultBO();
        exportResultBO.setTaskId(taskId);
        SpeechManagerListDO listDo = SpeechManagerConvertor.toDo(listBO);
        if (!isViewSelfRole(listBO.getLoginUserId())) {
            listDo.setCreator(null);
        }
        PageList<SpeechBaseEntity> speechBaseEntityPageList = speechBaseRepository.getManagerList(listDo, null);
        if (CollectionUtils.isEmpty(speechBaseEntityPageList.getData())) {
            exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            exportResultBO.setMessage("未查询到话术信息");
            saveExportResult(exportResultBO);
            return;
        }
        try {
            List<SpeechAnalysisExportDto> speechList = new ArrayList<>();
            for (SpeechBaseEntity speechBaseEntity : speechBaseEntityPageList.getData()) {
                List<SpeechNodeInterfaceVO> nodeList = new ArrayList<>();
                String flowFilePath = speechBaseEntity.getFlowFilePath();
                //下载话术流程
                byte[] data = fileDownloadUtils.downJsonFile(flowFilePath);
                String flowText = new String(data, StandardCharsets.UTF_8);
                FlowBaseConfig flowBaseConfig = JacksonUtils.readValue(flowText, FlowBaseConfig.class);
                for (Node node : flowBaseConfig.getNode()) {
                    if (node instanceof AnyMatchNode) {
                        continue;
                    }
                    if (node instanceof PlayRespondNode) {
                        if (node.getSetting().getNlp() != null && StringUtils.isNotBlank(node.getSetting().getNlp().getCode())) {
                            nodeList.add(new SpeechNodeInterfaceVO(node.getName(), node.getSetting().getNlp().getCode()));
                        }
                    }
                }
                if (CollectionUtils.isEmpty(nodeList)) {
                    continue;
                }
                List<String> interfaceCodeList = nodeList.stream().map(SpeechNodeInterfaceVO::getInterfaceCode).collect(Collectors.toList());
                List<NlpInterfaceDTO> interfaceList = nlpInterfaceService.queryInterfaceByCode(interfaceCodeList);
                Map<String, String> codeNodeNameMap = nodeList.stream().collect(Collectors.groupingBy(SpeechNodeInterfaceVO::getInterfaceCode,
                        Collectors.mapping(SpeechNodeInterfaceVO::getNodeName, Collectors.joining("、"))));
                Map<String, List<Map<String, String>>> analysisConfig = new HashMap<>();
                for (Map.Entry<String, String> entry : codeNodeNameMap.entrySet()) {
                    String interfaceCode = entry.getKey();
                    String nodeName = entry.getValue();
                    NlpInterfaceDTO nlpInterface = interfaceList.stream().filter(i -> interfaceCode.equals(i.getInterfaceCode())).findFirst().orElse(null);
                    if (nlpInterface == null) {
                        continue;
                    }
                    //数据转换
                    List<Map<String, String>> analysisConfigList = new ArrayList<>();
                    for (NlpInterfaceFieldDTO interfaceField : nlpInterface.getNlpInterfaceFieldEntityList()) {
                        Map<String, String> analysisMap = new HashMap<>();
                        String key = interfaceField.getInterfaceType() == 1 ? "intent" : interfaceField.getNlpNodeField();
                        if (CollectionUtils.isNotEmpty(interfaceField.getInterfaceFieldMapping())) {
                            String explain = interfaceField.getInterfaceFieldMapping().stream().map(NlpInterfaceFieldDetailDTO::getBusinessName)
                                    .filter(businessName -> !"default".equals(businessName)).distinct().collect(Collectors.joining("、"));
                            if (StringUtils.isEmpty(explain)) {
                                continue;
                            }
                            analysisMap.put(key, explain);
                        } else {
                            analysisMap.put(key, interfaceField.getFieldName());
                        }
                        String explain = StringUtils.isEmpty(interfaceField.getIntentSlotExplain()) ? interfaceField.getFieldName()
                                : interfaceField.getIntentSlotExplain();
                        analysisMap.put("信息项解释", explain);
                        analysisConfigList.add(analysisMap);
                    }
                    if (CollectionUtils.isNotEmpty(analysisConfigList)) {
                        analysisConfig.put(nodeName, analysisConfigList);
                    }
                }
                speechList.add(new SpeechAnalysisExportDto(speechBaseEntity.getId().toString(), speechBaseEntity.getSpeechName(),
                        JacksonUtils.writeValueAsString(analysisConfig)));
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream, SpeechAnalysisExportDto.class)
                    .registerWriteHandler(new CommonAbstractVerticalCellStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("话术解析配置导出").build();
            excelWriter.write(speechList, writeSheet);
            excelWriter.finish();
            InputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            String date = DateTime.now().toString(DateUtil.PATTERN_DEFAULT);
            String uploadPath = "file/" + "话术解析配置导出_" + date + ".xlsx";
            String url = fileUploadUtils.uploadByInputStream(byteArrayInputStream, uploadPath, ContainerParamEnum.ZHYL_AICC_UP_DOWN_FILE);
            url = fileUploadUtils.getCompleteUrl(url);
            if (StringUtils.isEmpty(url)) {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            } else {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.SUCCESS.getCode());
                exportResultBO.setUrl(url);
            }
        } catch (Exception e) {
            log.error("话术解析配置导出异常", e);
            exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            exportResultBO.setMessage(e.getMessage());
        }
        saveExportResult(exportResultBO);
    }

    @Override
    public void refreshSpeechType() {
        List<SpeechBaseEntity> speechList = speechBaseRepository.getAllPublishedSpeech();
        for (SpeechBaseEntity speechBaseEntity : speechList) {
            try {
                String flowFilePath = speechBaseEntity.getPublishFilePath();
                //下载话术流程
                byte[] data = fileDownloadUtils.downJsonFile(flowFilePath);
                String flowText = new String(data, StandardCharsets.UTF_8);
                FlowBaseConfig flowBaseConfig = JacksonUtils.readValue(flowText, FlowBaseConfig.class);
                if (flowBaseConfig == null || CollectionUtils.isEmpty(flowBaseConfig.getNode())) {
                    continue;
                }
                //获取话术类型和大模型阶段
                setSpeechModelType(speechBaseEntity, flowBaseConfig);
                speechBaseRepository.updateSpeechMode(speechBaseEntity);
            } catch (Exception e) {
                log.error("话术模式更新异常,speechId:{}", speechBaseEntity.getId(), e);
            }
        }
    }

    @Override
    @Async
    public void generateNodeExplain(SpeechLinkExplainDto speechLinkExplainDto, AsyncResultVo<Object> asyncResultVo) {
        try {
            redisCacheService.setValueAndExpire(RedisCacheKey.SPEECH_EXPORT + speechLinkExplainDto.getTaskId(),
                    JacksonUtils.writeValueAsString(asyncResultVo), 10L, TimeUnit.MINUTES);
            String id = speechLinkExplainDto.getId();
            Integer speechKinds = speechLinkExplainDto.getSpeechKinds();
            String flowFilePath = null;
            if (Objects.isNull(speechKinds) || speechKinds == 0) {
                SpeechBaseEntity speechBaseEntity = speechBaseRepository.getManagerById(Long.valueOf(id));
                if (Objects.isNull(speechBaseEntity)) {
                    throw new MedicalBusinessException("话术不存在");
                }
                flowFilePath = speechBaseEntity.getFlowFilePath();
            } else {
                SpeechFragmentEntity speechFragment = speechFragmentRepository.getById(Long.valueOf(id));
                if (Objects.isNull(speechFragment)) {
                    throw new MedicalBusinessException("话术片段不存在");
                }
                flowFilePath = speechFragment.getFlowFilePath();

            }
            byte[] data = fileDownloadUtils.downJsonFile(flowFilePath);
            String flowText = new String(data, StandardCharsets.UTF_8);
            //解析json
            FlowBaseConfig flowBaseConfig = JacksonUtils.readValue(flowText, FlowBaseConfig.class);
            List<Node> nodeList = flowBaseConfig.getNode();
            List<String> nodeIds = speechLinkExplainDto.getNodeIds();
            if (!CollectionUtils.isEmpty(nodeIds)) {
                nodeList = nodeList.stream().filter(node -> nodeIds.contains(node.getId())).collect(Collectors.toList());
            }
            LlmTemplateEntity llmTemplate = llmTemplateRepository.getLlmTemplateByType(4);
            if (Objects.isNull(llmTemplate)) {
                throw new MedicalBusinessException("llmTemplate不存在");
            }
            Map<String, String> commonNodeMap = getDictNameMap();
            String templateContent = llmTemplate.getTemplateContent();
            Map<String, String> result = new HashMap<>();
            for (Node node : nodeList) {
                Map<String, String> linkNameExplain = getLinkNameExplain(node, templateContent, id, flowBaseConfig.getLlmVersionMap(), commonNodeMap);
                result.putAll(linkNameExplain);
            }
            asyncResultVo.setCode(SpeechManagerEnums.ExportResultEnum.SUCCESS.getCode());
            asyncResultVo.setData(result);
        } catch (Exception e) {
            log.error("节点说明生成失败 error", e);
            asyncResultVo.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            asyncResultVo.setMessage(e.getMessage());
        }
        redisCacheService.setValueAndExpire(RedisCacheKey.SPEECH_EXPORT + speechLinkExplainDto.getTaskId(),
                JacksonUtils.writeValueAsString(asyncResultVo), 10L, TimeUnit.MINUTES);
    }

    @Override
    public AsyncResultVo<Map<String, String>> getSpeechLinkExplain(String taskId) {
        String cacheValue = redisCacheService.getCacheValue(RedisCacheKey.SPEECH_EXPORT + taskId);
        return JacksonUtils.readValue(cacheValue, new TypeReference<AsyncResultVo<Map<String, String>>>() {
        });
    }

    private Map<String, String> getLinkNameExplain(Node node, String templateContent, String id, Map<String, String> llmVersionMap,
                                                   Map<String, String> commonNodeMap) {
        Map<String, String> result = new HashMap<>();
        if (node instanceof PlayNode) {
            List<NodePrompt> promptList = node.getPromptList();
            if (CollectionUtils.isEmpty(promptList)) {
                log.info("SpeechFlowManagerServiceImpl[getLinkNameExplain] 没有提示音:{}", node.getName());
                return result;
            }
            NodePrompt nodePrompt = promptList.get(0);
            String playContent = nodePrompt.getText();
            // key: linkerId value:大模型需要的节点名称
            Map<String, String> linkerIdNameMap = getLinkerIdNameMap(node);
            if (MapUtils.isEmpty(linkerIdNameMap)) {
                return result;
            }
            // 名称中包含分隔符的需要分开处理,过滤公共节点，不传大模型
            List<String> nameList = filterCommonName(commonNodeMap, result, linkerIdNameMap);
            if (CollectionUtils.isEmpty(nameList)) {
                log.info("getLinkNameExplain,均为公共节点，无需调用大模型,nodeId:{}, nodeName:{}", node.getId(), linkerIdNameMap.values());
                return result;
            }
            String linkName = String.join("\n", nameList);

            //调用大模型
            String questionContent = templateContent.replace("${playContent}", playContent).replace("${linkNames}", linkName);
            LlmRequestBO llmRequestBO = new LlmRequestBO(null, (PlayNode) node);
            FlowSession session = new FlowSession();
            prepareInit((PlayNode) node, session, id, llmVersionMap);
            LlmService service = llmServiceFactory.getService(llmVersionMap, Constants.AI_MODEL_NODE_GENERATE);
            LlmResultBO sparkLlmResultBO = service.callLlm(questionContent, null, llmRequestBO, session, AI_MODEL_NODE_GENERATE);
            String nodeNameResult = Optional.ofNullable(sparkLlmResultBO).map(LlmResultBO::getText).orElse("");
            if (StringUtils.isEmpty(nodeNameResult)) {
                log.info("nodeId:{}, 生成节点入参：{}", node.getId(), questionContent);
                return result;
            }
            //解析模型返回内容，key:输入名称 value:输出描述
            Map<String, String> linkNameExplain = getLinkNameExplain(nodeNameResult);
            for (Map.Entry<String, String> entry : linkerIdNameMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                String explain = linkNameExplain.get(value);
                if (StringUtils.isNotBlank(explain)) {
                    //模型输出描述
                    result.put(key, explain);
                } else if (commonNodeMap.containsKey(value)) {
                    //通用节点描述
                    result.put(key, commonNodeMap.get(value));
                } else {
                    //名称中包含分隔符的需要分开处理，模型无输出判断是否为通用节点
                    String[] nodeNames = value.split("[/|\\\\]");
                    List<String> explainList = new ArrayList<>();
                    for (String nodeName : nodeNames) {
                        String s = linkNameExplain.get(nodeName);
                        if (StringUtils.isNotBlank(s)) {
                            explainList.add(s);
                            continue;
                        }
                        String cname = commonNodeMap.get(nodeName);
                        if (StringUtils.isNotBlank(cname)) {
                            explainList.add(cname);
                        }
                    }
                    String explainStr = String.join(",", explainList);
                    //除了第一个若字，其他若字替换成或字
                    explainStr = explainStr.replaceAll("若", "或");
                    explainStr = explainStr.replaceFirst("或", "若");
                    result.put(key, explainStr);
                }
            }
            log.info("getLinkNameExplain,nodeId:{}, 入参:{},大模型返回结果:{},处理后结果:{}", node.getId(), linkerIdNameMap.values(),
                    nodeNameResult, JacksonUtils.writeValueAsString(linkNameExplain));
        }
        return result;
    }

    @NotNull
    private static List<String> filterCommonName(Map<String, String> commonNodeMap, Map<String, String> result, Map<String, String> linkerIdNameMap) {
        List<String> nameList = new ArrayList<>();
        Iterator<Map.Entry<String, String>> iterator = linkerIdNameMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            String linkerId = entry.getKey();
            String linkerName = entry.getValue();
            //若节点名称直接为公共节点则过滤，直接使用公共节点描述
            if (commonNodeMap.containsKey(linkerName)) {
                result.put(linkerId, commonNodeMap.get(linkerName));
                iterator.remove();
                continue;
            }
            //节点名称包含分隔符，则需分开判断
            String[] linkerNameArr = linkerName.split("[/|\\\\]");
            List<String> notCommonList = Arrays.stream(linkerNameArr).filter(name -> !commonNodeMap.containsKey(name)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(notCommonList)) {
                String explainStr = String.join(",", notCommonList);
                //除了第一个若字，其他若字替换成或字
                explainStr = explainStr.replaceAll("若", "或");
                explainStr = explainStr.replaceFirst("或", "若");
                result.put(linkerId, explainStr);
                iterator.remove();
                continue;
            }
            nameList.addAll(notCommonList);
        }
        return nameList;
    }

    private Map<String, String> getDictNameMap() {
        DictPackageVO dictByValue = systemDictService.getDictByValue(Constants.KNOWLEDGE_NAME_EXPLAIN);
        if (Objects.nonNull(dictByValue)) {
            List<DictPackageVO> child = dictByValue.getChild();
            return child.stream().collect(Collectors.toMap(DictPackageVO::getDictName, DictPackageVO::getDictValue));
        }
        return new HashMap<>();
    }

    @NotNull
    private static Map<String, String> getLinkNameExplain(String nodeNameResult) {
        Map<String, String> linkNameExplain = new HashMap<>();

        Matcher matcher = LINKNAME_EXPLAIN_PATTERN.matcher(nodeNameResult);
        // 使用Matcher对象遍历所有匹配项
        while (matcher.find()) {
            // 提取匹配的组
            String key = matcher.group(1).trim(); // 状态
            String value = matcher.group(3).trim(); // 描述
            // 将键值对添加到Map中
            linkNameExplain.put(key, value);
        }
        return linkNameExplain;
    }

    private void prepareInit(PlayNode node, FlowSession session, String id, Map<String, String> llmVersionMap) {
        NodeSetting nodeSetting = new NodeSetting();
        nodeSetting.setInterrupt(false);
        node.setSetting(nodeSetting);
        session.setCallId(uidService.getUID());
        session.setSpeechId(Long.valueOf(id));
        InvokeMessageDto invokeMessageDto = new InvokeMessageDto();
        invokeMessageDto.setSeqNo(0);
        session.setInvokeMessage(invokeMessageDto);
        session.setLlmVersionMap(llmVersionMap);
    }

    @NotNull
    private static Map<String, String> getLinkerIdNameMap(Node node) {
        // key: linkerId value:大模型需要的节点名称
        Map<String, String> linkerIdNameMap = new HashMap<>();
        List<ConditionLink> linkers = node.getLinkers();
        for (ConditionLink linker : linkers) {
            boolean nameExplainLock = linker.getNameExplainLock();
            if (nameExplainLock) {
                continue;
            }
            boolean nameExplainEdit = linker.getNameExplainEdit();
            if (nameExplainEdit) {
                continue;
            }
            String name = linker.getName();
            String condition = linker.getCondition();
            if (!StringUtils.isEmpty(condition)) {
                List<String> conditionNameList = new ArrayList<>();
                Matcher matcher = CONDITION_NAME_PATTERN.matcher(condition);
                while (matcher.find()) {
                    if (StringUtils.isNotBlank(matcher.group(1))) {
                        conditionNameList.add(matcher.group(1));
                    }
                }
                if (conditionNameList.size() == 2 || conditionNameList.size() == 3) {
                    name = String.join("/", conditionNameList);
                }
            }
            linkerIdNameMap.put(linker.getId(), name);
        }
        return linkerIdNameMap;
    }

    public List<File> getFileList(SpeechManagerExportEntity entity) throws IOException {
        List<File> fileList = new ArrayList<>();
        //话术生成文件
        File file = new File(entity.getSpeechName() + ".txt");
        FileUtils.writeStringToFile(file, JacksonUtils.writeValueAsString(entity), "UTF-8");
        fileList.add(file);
        //下载话术中包含的音频文件
        Map<String, File> audioFiles = downLoadAudio(AesEncryptUtil.desEncrypt(entity.getFlowJson()));
        if (MapUtils.isNotEmpty(audioFiles)) {
            for (Map.Entry<String, File> fileEntry : audioFiles.entrySet()) {
                String fileName = fileEntry.getKey();
                File originFile = fileEntry.getValue();
                File newFile = new File(fileName);
                originFile.renameTo(newFile);
                fileList.add(newFile);
            }
        }
        return fileList;
    }


    private void publishToOperationManagement(SpeechBaseEntity speechBaseEntity, FlowBaseConfig flowBaseConfig) {
        SpeechCraftRequest speechCraftRequest = getSpeechCraftDto(speechBaseEntity, flowBaseConfig);
        nlpInterfaceService.publishToOperationManagement(speechCraftRequest, speechBaseEntity.getUpdatedBy());
    }

    private SpeechCraftRequest getSpeechCraftDto(SpeechBaseEntity speechBaseEntity, FlowBaseConfig flowBaseConfig) {
        SpeechCraftRequest speechCraftRequest = new SpeechCraftRequest();
        speechCraftRequest.setSpeechName(speechBaseEntity.getSpeechName());
        speechCraftRequest.setSystemSpeechName(speechBaseEntity.getSpeechName());
        speechCraftRequest.setSpeechIntro(speechBaseEntity.getSpeechIntro());
        speechCraftRequest.setSpeechMode(speechBaseEntity.getSpeechMode());
        speechCraftRequest.setModelStage(speechBaseEntity.getModelStage());

        SpeechBusinessRelEntityRequest speechBusinessRelEntityRequest = new SpeechBusinessRelEntityRequest();
        speechBusinessRelEntityRequest.setSpeechPlat(5);
        speechBusinessRelEntityRequest.setSpeechCraftId(speechBaseEntity.getId().toString());
        //获取标签配置
        Map<String, String> labelConfig = new HashMap<>();
        //获取节点配置
        Map<String, Integer> nodeInterface = new HashMap<>();
        //节点排序
        Map<String, Integer> nodeSort = new HashMap<>();
        //获取变量对应放音
        Map<String, String> variableMapping = new HashMap<>();
        //获取节点配置和标签配置
        buildNodeInterfaceAndConfig(flowBaseConfig, labelConfig, nodeInterface, nodeSort, variableMapping);
        String normalAnswer = labelConfig.get(FlowLabelEnum.NORMAL_ANSWER.getCode());
        if (!StringUtils.isEmpty(normalAnswer)) {
            speechBusinessRelEntityRequest.setNormalAnswer(normalAnswer);
        }
        String completedAnswer = labelConfig.get(FlowLabelEnum.COMPLETED_ANSWER.getCode());
        if (!StringUtils.isEmpty(completedAnswer)) {
            speechBusinessRelEntityRequest.setCompleteAnswer(completedAnswer);
        }
        speechBusinessRelEntityRequest.setNodeInterface(JSON.toJSONString(nodeInterface));
        speechBusinessRelEntityRequest.setNodeSort(JSON.toJSONString(nodeSort));

        //获取发音人配置
        VoiceConfigEntity voiceConfigEntity = getVoiceConfigEntity(speechBaseEntity.getId());
        if (Objects.nonNull(voiceConfigEntity)) {
            speechBusinessRelEntityRequest.setRobotVoiceId(voiceConfigEntity.getTtsVoiceCode());
            speechBusinessRelEntityRequest.setSpeedRate(voiceConfigEntity.getTtsRate());
        }
        speechCraftRequest.setPlatConfig(Collections.singletonList(speechBusinessRelEntityRequest));
        //查询话术变量
        List<JSONObject> speechContents = getSpeechContents(speechBaseEntity, variableMapping);
        speechCraftRequest.setSpeechContents(JSON.toJSONString(speechContents));
        //创建人不是交付管理员都归到话术配置管理员
        String createdBy = speechBaseEntity.getCreatedBy();
        Boolean deliveryRole = deliveryRole(createdBy);
        String roleName = deliveryRole ? Constants.DELIVERY_CONFIG_ROLE : Constants.SPEECH_CONFIG_ROLE;
        speechCraftRequest.setRoleName(roleName);
        speechCraftRequest.setSpeechType(speechBaseEntity.getSpeechType());
        //获取话术业务采集配置
        speechCraftRequest.setAnalysisMode(flowBaseConfig.isLlmAnalysisSwitch() ? 2 : 1);
        //处理异常标签
        SpeechAbnormalLabelSaveRequest speechAbnormalLabelSaveRequest = getAbnormalLabelSaveParams(flowBaseConfig);
        speechCraftRequest.setSpeechAbnormalLabelSaveParam(speechAbnormalLabelSaveRequest);
        //大模型离线解析版本
        String llmAnalysisVersion = getLlmAnalysisVersion(flowBaseConfig);
        speechCraftRequest.setLlmAnalysisVersion(llmAnalysisVersion);
        //设置 应用场景 业务方向不 标签分类  定制化需求，不具备通用性
        buildLabelAndTag(speechBaseEntity, speechCraftRequest);
        return speechCraftRequest;
    }


    /**
     * 应用场景 业务方向 标签分类  定制化需求，不具备通用性
     *
     * @param speechBaseEntity
     * @param speechCraftRequest
     */
    private void buildLabelAndTag(SpeechBaseEntity speechBaseEntity, SpeechCraftRequest speechCraftRequest) {
        //业务方向
        String businessDirection = speechBaseEntity.getBusinessDirection();
        speechCraftRequest.setBusinessDirection(speechBaseEntity.getBusinessDirection());
        Map<Long, SpeechSceneConfigEntity> speechSceneMap = getSpeechSceneConfigMap();
        String speechScene = speechBaseEntity.getSpeechScene();
        List<List<Long>> sceneConfigs = HierarchyUtil.parseData(speechScene);

        Set<Set<Long>> speechLabels = new HashSet<>();
        //通用/定制 -》标签
        String range = speechBaseEntity.getRange();
        if (!StringUtils.isEmpty(range)) {
            buildLabel(range, speechSceneMap, speechLabels);
        }

        // 卫生方向 [[125,146],[125,147],[126],[130,151],[130,152]]
        if ("121".equals(businessDirection)) {
            String speechTag = null;
            Set<Long> applyScenes = new HashSet<>();
            for (List<Long> sceneConfig : sceneConfigs) {
                int size = sceneConfig.size();
                Long id = sceneConfig.get(0);
                SpeechSceneConfigEntity speechSceneConfigEntity = speechSceneMap.get(id);
                //应用场景
                if (size == 1) {
                    speechTag = getSpeechTag(speechSceneConfigEntity);
                    continue;
                }
                //慢病随访下面的高血压、糖尿病场景；
                if (id == 125L) {
                    speechTag = speechSceneConfigEntity.getOperationValue();
                    id = sceneConfig.get(1);
                    speechSceneConfigEntity = speechSceneMap.get(id);
                    if (Objects.isNull(speechSceneConfigEntity)) {
                        continue;
                    }
                    applyScenes.add(speechSceneConfigEntity.getOperationId());
                }
                //基层考核 下面的标签
                if (id == 130) {
                    speechTag = speechSceneConfigEntity.getOperationValue();
                    id = sceneConfig.get(1);
                    speechSceneConfigEntity = speechSceneMap.get(id);
                    if (Objects.isNull(speechSceneConfigEntity)) {
                        continue;
                    }
                    buildLabel(speechSceneConfigEntity, speechLabels);
                }
            }
            speechCraftRequest.setApplyScene(Joiner.on(",").join(applyScenes));
            speechCraftRequest.setSpeechLabel(JSON.toJSONString(speechLabels));
            if (!StringUtils.isEmpty(speechTag)) {
                speechCraftRequest.setSpeechTag(Integer.valueOf(speechTag));
            }
            log.info("SpeechFlowManagerServiceImpl[卫生话术标签同步] speechCraftRequest:{}", JSON.toJSONString(speechCraftRequest));
            return;
        }
        // 医院 [[123,158,159],[123,183,184]]
        if ("122".equals(businessDirection)) {
            String speechTag = null;
            Set<Long> applyScenes = new HashSet<>();
            for (List<Long> sceneConfig : sceneConfigs) {
                int size = sceneConfig.size();
                Long id = sceneConfig.get(0);
                SpeechSceneConfigEntity speechSceneConfigEntity = speechSceneMap.get(id);
                //应用场景
                if (size == 1) {
                    speechTag = getSpeechTag(speechSceneConfigEntity);
                    continue;
                }
                //标签
                speechTag = speechSceneConfigEntity.getOperationValue();
                id = sceneConfig.get(1);
                speechSceneConfigEntity = speechSceneMap.get(id);
                if (Objects.isNull(speechSceneConfigEntity)) {
                    continue;
                }
                buildLabel(speechSceneConfigEntity, speechLabels);

            }
            speechCraftRequest.setApplyScene(Joiner.on(",").join(applyScenes));
            speechCraftRequest.setSpeechLabel(JSON.toJSONString(speechLabels));
            if (!StringUtils.isEmpty(speechTag)) {
                speechCraftRequest.setSpeechTag(Integer.valueOf(speechTag));
            }
        }
        log.info("SpeechFlowManagerServiceImpl[医院话术标签同步] speechCraftRequest:{}", JSON.toJSONString(speechCraftRequest));
    }

    private static void buildLabel(SpeechSceneConfigEntity speechSceneConfigEntity, Set<Set<Long>> speechLabels) {
        Long operationParentId = speechSceneConfigEntity.getOperationParentId();
        Long operationId = speechSceneConfigEntity.getOperationId();
        Set<Long> tempLabels = new HashSet<>();
        tempLabels.add(operationParentId);
        tempLabels.add(operationId);
        speechLabels.add(tempLabels);
    }

    private static void buildLabel(String range, Map<Long, SpeechSceneConfigEntity> speechSceneMap, Set<Set<Long>> speechLabels) {
        List<List<Long>> lists = HierarchyUtil.parseData(range);
        for (List<Long> list : lists) {
            Set<Long> tempLabels = new HashSet<>();
            for (Long id : list) {
                SpeechSceneConfigEntity speechSceneConfigEntity = speechSceneMap.get(id);
                if (Objects.isNull(speechSceneConfigEntity)) {
                    continue;
                }
                Long operationParentId = speechSceneConfigEntity.getOperationParentId();
                Long operationId = speechSceneConfigEntity.getOperationId();
                tempLabels.add(operationParentId);
                tempLabels.add(operationId);
            }
            speechLabels.add(tempLabels);
        }
    }

    private static String getSpeechTag(SpeechSceneConfigEntity speechSceneConfigEntity) {
        if (Objects.isNull(speechSceneConfigEntity)) {
            return null;
        }
        Integer operationType = speechSceneConfigEntity.getOperationType();
        //都归为其他
        if (Objects.isNull(operationType)) {
            return "10";
        }
        return speechSceneConfigEntity.getOperationValue();
    }

    private String getLlmAnalysisVersion(FlowBaseConfig flowBaseConfig) {
        Map<String, String> llmVersionMap = flowBaseConfig.getLlmVersionMap();
        if (MapUtils.isEmpty(llmVersionMap) || StringUtils.isEmpty(llmVersionMap.get(Constants.AI_MODEL_NODE_LLMANALYSIS))) {
            DictPackageVO llmVersionDict = systemDictService.getDictByValue(Constants.AI_MODEL_NODE_LLMANALYSIS);
            return llmVersionDict.getChild().get(0).getDictValue();
        }
        return llmVersionMap.get(Constants.AI_MODEL_NODE_LLMANALYSIS);
    }

    private static SpeechAbnormalLabelSaveRequest getAbnormalLabelSaveParams(FlowBaseConfig flowBaseConfig) {
        Map<String, SpeechAbnormalLabelSaveParam> speechAbnormalLabelSaveParamMap = flowBaseConfig.getSpeechAbnormalLabelSaveParamMap();
        List<SpeechAbnormalLabelParam> speechAbnormalLabelParams = new ArrayList<>();
        if (MapUtils.isNotEmpty(speechAbnormalLabelSaveParamMap)) {
            for (SpeechAbnormalLabelSaveParam speechAbnormalLabelSaveParam : speechAbnormalLabelSaveParamMap.values()) {
                List<SpeechAbnormalLabelParam> speechLabels = speechAbnormalLabelSaveParam.getSpeechLabels();
                if (CollectionUtils.isEmpty(speechLabels)) {
                    continue;
                }
                //过滤掉空标签
                List<SpeechAbnormalLabelParam> tempSpeechLabels = speechLabels.stream().filter(label ->
                                StringUtils.isNotEmpty(label.getLabelName()) &&
                                        StringUtils.isNotEmpty(label.getAbnormalType()) &&
                                        StringUtils.isNotEmpty(label.getAbnormalReason()) &&
                                        (StringUtils.isNotEmpty(label.getArtificialIntervention()) || StringUtils.isNotEmpty(label.getAiPreprocessing()))).
                        collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tempSpeechLabels)) {
                    speechAbnormalLabelParams.addAll(tempSpeechLabels);
                }
            }
        }
        //合并相同标签
        return mergeLabel(speechAbnormalLabelParams, flowBaseConfig.getId());
    }

    public static SpeechAbnormalLabelSaveRequest mergeLabel(List<SpeechAbnormalLabelParam> speechAbnormalLabelParams, String speechId) {
        Map<String, SpeechAbnormalLabelParam> mergedParams = new HashMap<>();
        if (CollectionUtils.isNotEmpty(speechAbnormalLabelParams)) {
            for (SpeechAbnormalLabelParam speechAbnormalLabelParam : speechAbnormalLabelParams) {
                String unionKey = getUnionKey(speechAbnormalLabelParam);
                if (mergedParams.containsKey(unionKey)) {
                    // 合并 labelItems
                    SpeechAbnormalLabelParam existingParam = mergedParams.get(unionKey);
                    existingParam.getLabelItems().addAll(speechAbnormalLabelParam.getLabelItems());
                } else {
                    // 创建新的 SpeechAbnormalLabelParam 对象
                    mergedParams.put(unionKey, speechAbnormalLabelParam);
                }
            }
            List<SpeechAbnormalLabelParam> abnormalLabelParams = new ArrayList<>(mergedParams.values());
            for (SpeechAbnormalLabelParam abnormalLabelParam : mergedParams.values()) {
                List<SpeechAbnormalLabelItemParam> labelItems = abnormalLabelParam.getLabelItems();
                labelItems = labelItems.stream().distinct().collect(Collectors.toList());
                abnormalLabelParam.setLabelItems(labelItems);
                abnormalLabelParams.add(abnormalLabelParam);
            }
            abnormalLabelParams = abnormalLabelParams.stream().distinct().collect(Collectors.toList());
            SpeechAbnormalLabelSaveRequest speechAbnormalLabelSaveRequest = new SpeechAbnormalLabelSaveRequest();
            speechAbnormalLabelSaveRequest.setSpeechId(Long.valueOf(speechId));
            speechAbnormalLabelSaveRequest.setSpeechLabels(abnormalLabelParams);
            return speechAbnormalLabelSaveRequest;
        }
        return new SpeechAbnormalLabelSaveRequest();
    }

    @NotNull
    private static String getUnionKey(SpeechAbnormalLabelParam speechAbnormalLabelParam) {
        String labelName = speechAbnormalLabelParam.getLabelName();
        String abnormalType = speechAbnormalLabelParam.getAbnormalType();
        String abnormalReason = speechAbnormalLabelParam.getAbnormalReason();
        String artificialIntervention = speechAbnormalLabelParam.getArtificialIntervention();
        String aiPreprocessing = speechAbnormalLabelParam.getAiPreprocessing();
        return labelName + abnormalType + abnormalReason + artificialIntervention + aiPreprocessing;
    }

    /**
     * 【需求】：在能力平台的判断节点中打标签，运管向下一级放音节点打该标签；（标签包含：完整回答、正常回答）
     */
    private void buildNodeInterfaceAndConfig(FlowBaseConfig flowBaseConfig, Map<String, String> labelConfig,
                                             Map<String, Integer> nodeInterface, Map<String, Integer> nodeSort,
                                             Map<String, String> variableMapping) {
        Map<String, Node> nodeMap = flowCacheManager.getNodeMap(flowBaseConfig);
        //正常回答nodeId
        Set<String> normalAnswerList = new HashSet<>();
        //完整回答nodeId
        Set<String> completeAnswerList = new HashSet<>();
        LinkedHashMap<String, String> linkParentNodeMap = new LinkedHashMap<>();
        LinkedHashMap<String, ConditionLink> allLinkMap = new LinkedHashMap<>();
        buildMap(nodeMap.values(), linkParentNodeMap, allLinkMap);
        for (Node node : nodeMap.values()) {
            List<NodeTag> tags = node.getTags();
            //放音节点
            if (CollectionUtils.isNotEmpty(tags)) {
                //处理节点标签
                handleNodeTags(node, tags, normalAnswerList, completeAnswerList);
            }
            //处理语义接口
            handleNodeInterFace(nodeInterface, nodeSort, node);
            //处理变量
            handleVariable(variableMapping, node, nodeMap, linkParentNodeMap, allLinkMap);
            // 处理判断节点标签
            List<ConditionLink> linkers = node.getLinkers();
            if (CollectionUtils.isNotEmpty(linkers)) {
                handleLinkTags(linkers, nodeMap, normalAnswerList, completeAnswerList);
                handleVariable(variableMapping, linkers);
            }
        }
        //说明勾选了完整回答
        if (!CollectionUtils.isEmpty(normalAnswerList)) {
            //查询话术内和话术外的号码错误，居民已死亡 知识库 放到完整回答中
            labelConfig.put(FlowLabelEnum.NORMAL_ANSWER.getCode(), String.join(",", normalAnswerList));
            handleKnowledge(flowBaseConfig, completeAnswerList, normalAnswerList);
        }
        if (!CollectionUtils.isEmpty(completeAnswerList)) {
            //查询话术内和话术外的号码错误，居民已死亡 知识库 放到完整回答中
            handleKnowledge(flowBaseConfig, completeAnswerList, normalAnswerList);
            labelConfig.put(FlowLabelEnum.COMPLETED_ANSWER.getCode(), String.join(",", completeAnswerList));
        }
    }

    public static void handleVariable(Map<String, String> variableMapping, List<ConditionLink> linkers) {
        for (ConditionLink linker : linkers) {
            List<NodePrompt> prompts = linker.getPrompts();
            if (CollectionUtils.isNotEmpty(prompts)) {
                for (NodePrompt prompt : prompts) {
                    String text = prompt.getText();
                    Matcher matcher = VARIABLE_PATTERN.matcher(text);
                    while (matcher.find()) {
                        variableMapping.put(matcher.group(1), text);
                    }
                }
            }
        }
    }

    public static void handleVariable(Map<String, String> variableMapping, Node node,
                                      Map<String, Node> nodeMap, LinkedHashMap<String, String> linkParentNodeMap,
                                      LinkedHashMap<String, ConditionLink> allLinkMap) {
        List<NodePrompt> promptList = node.getPromptList();
        if (CollectionUtils.isNotEmpty(promptList)) {
            NodePrompt nodePrompt = promptList.get(0);
            String text = nodePrompt.getText();
            Matcher matcher = VARIABLE_PATTERN.matcher(text);
            while (matcher.find()) {
                String group = matcher.group(1);
                try {
                    boolean startNode = node instanceof StartNode;
                    if (startNode) {
                        variableMapping.put(group, text);
                        continue;
                    }
                    boolean containsKey = variableMapping.containsKey(group);
                    if (containsKey) {
                        containsKeyVariable(variableMapping, node, group, text, nodeMap, linkParentNodeMap, allLinkMap);
                        continue;
                    }
                } catch (Exception e) {
                    log.error("SpeechFlowManagerServiceImpl[handleVariable] node:{}", node.getId(), e);
                }
                variableMapping.put(group, text);
            }
        }
    }

    /**
     * 变量名相同，取是本人分支下面的放音内容
     *
     * @param variableMapping
     * @param node
     * @param group
     * @param text
     * @param nodeMap
     * @param linkParentNodeMap
     * @param allLinkMap
     */
    private static void containsKeyVariable(Map<String, String> variableMapping, Node node, String group, String text,
                                            Map<String, Node> nodeMap, LinkedHashMap<String, String> linkParentNodeMap,
                                            LinkedHashMap<String, ConditionLink> allLinkMap) {
        //相同变量，取 (current.selfIntroduction == '是本人') 下面的放音
        ConditionLink conditionLink = null;
        //找到是本人的分支开始
        for (ConditionLink linker : allLinkMap.values()) {
            String condition = linker.getCondition();
            if (StringUtils.isEmpty(condition)) {
                continue;
            }
            condition = condition.replaceAll("\\s*", "");
            if (condition.contains("(current.selfIntroduction=='是本人')")) {
                conditionLink = linker;
                break;
            }
        }
        //判断当前放音是不是在是本人的分支下面
        if (conditionLink != null) {
            Node parentNode = node;
            while (true) {
                ConditionLink parentLink = findParenLink(parentNode, allLinkMap);
                if (parentLink == null) {
                    break;
                }
                if (parentLink.getId().equals(conditionLink.getId())) {
                    variableMapping.put(group, text);
                    break;
                }
                String parentNodeId = linkParentNodeMap.get(parentLink.getId());
                parentNode = nodeMap.get(parentNodeId);
            }
        }
    }

    public static void buildMap(Collection<Node> nodeList, LinkedHashMap<String, String> linkParentNodeMap, LinkedHashMap<String, ConditionLink> allLinkMap) {
        for (Node node : nodeList) {
            String id = node.getId();
            List<ConditionLink> linkers = node.getLinkers();
            if (!CollectionUtils.isEmpty(linkers)) {
                for (ConditionLink linker : linkers) {
                    //判断节点的放音map
                    linkParentNodeMap.put(linker.getId(), id);
                    allLinkMap.put(linker.getId(), linker);
                }
            }
        }
    }

    /**
     * 寻找当前放音的父节点
     *
     * @param node
     * @param allLinkMap
     * @return
     */
    private static ConditionLink findParenLink(Node node,
                                               LinkedHashMap<String, ConditionLink> allLinkMap) {
        //先找直接连接的
        for (ConditionLink conditionLink : allLinkMap.values()) {
            String toNode = conditionLink.getToId();
            if (toNode.equals(node.getId())) {
                return conditionLink;
            }
        }
        //没有再找跳转连接的
        for (ConditionLink conditionLink : allLinkMap.values()) {
            String toNode = conditionLink.getToNode();
            if (toNode.equals(node.getId())) {
                return conditionLink;
            }
        }
        return null;
    }

    public static void handleNodeInterFace(Map<String, Integer> nodeInterface, Map<String, Integer> nodeSort, Node node) {
        Optional.ofNullable(node)
                .map(Node::getSetting)
                .map(NodeSetting::getNlp)
                .ifPresent(nlp -> {
                    if (StringUtils.isNotEmpty(nlp.getCode())) {
                        nodeInterface.put(node.getId(), Integer.valueOf(nlp.getId()));
                        nodeSort.put(node.getId(), node.getLevel());
                    }
                });
    }

    private void handleKnowledge(FlowBaseConfig flowBaseConfig, Set<String> completeAnswerList, Set<String> normalAnswerList) {
        //话术内知识库
        List<SpeechKnowledgeParam> speechKnowledgeParamList = flowBaseConfig.getSpeechKnowledgeParamList();
        if (CollectionUtils.isNotEmpty(speechKnowledgeParamList)) {
            for (SpeechKnowledgeParam speechKnowledgeParam : speechKnowledgeParamList) {
                String knowledgeName = speechKnowledgeParam.getName();
                if (NODENAMELIST.contains(knowledgeName)) {
                    completeAnswerList.add(speechKnowledgeParam.getUid());
                    normalAnswerList.add(speechKnowledgeParam.getUid());
                }
            }
        }
        //全局知识库
        List<SpeechKnowledgeEntity> speechKnowledgeConfigList = speechKnowledgeRepository.getSpeechKnowledgeConfigList(0);
        if (CollectionUtils.isNotEmpty(speechKnowledgeConfigList)) {
            for (SpeechKnowledgeEntity speechKnowledgeEntity : speechKnowledgeConfigList) {
                String knowledgeName = speechKnowledgeEntity.getName();
                if (NODENAMELIST.contains(knowledgeName)) {
                    completeAnswerList.add(speechKnowledgeEntity.getId().toString());
                    normalAnswerList.add(speechKnowledgeEntity.getId().toString());
                }
            }
        }
    }

    /**
     * 处理判断节点标签
     *
     * @param linkers            判断节点
     * @param nodeMap            节点集合
     * @param normalAnswerList   正常回答集合
     * @param completeAnswerList 完整回答集合
     */
    private static void handleLinkTags(List<ConditionLink> linkers, Map<String, Node> nodeMap, Set<String> normalAnswerList, Set<String> completeAnswerList) {
        //判断节点有标签，取跳转节点的id
        for (ConditionLink linker : linkers) {
            List<NodeTag> linkerTags = linker.getTags();
            if (CollectionUtils.isEmpty(linkerTags)) {
                continue;
            }
            String toNodeId = linker.getToNode();
            Node node = nodeMap.get(toNodeId);
            Integer type = node.getType();
            String nodeId = node.getId();
            boolean endNode = Objects.equals(type, PlayNodeEnum.NodeTypeEnum.END_NODE.getCode());
            setNodeIdToNormalAndFullAnswers(linkerTags, normalAnswerList, nodeId, endNode, completeAnswerList);
        }
    }

    /**
     * 获取放音节点标签所属的节点id
     *
     * @param node               节点
     * @param tags               节点标签
     * @param normalAnswerList   正常回答集合
     * @param completeAnswerList 完整回答集合
     */
    private static void handleNodeTags(Node node, List<NodeTag> tags, Set<String> normalAnswerList, Set<String> completeAnswerList) {
        Integer type = node.getType();
        String nodeId = node.getId();
        //结束节点
        boolean endNode = Objects.equals(type, PlayNodeEnum.NodeTypeEnum.END_NODE.getCode());
        setNodeIdToNormalAndFullAnswers(tags, normalAnswerList, nodeId, endNode, completeAnswerList);
    }

    /**
     * 将节点id分发到正常回答和完整回答集合
     *
     * @param tags
     * @param normalAnswerList
     * @param nodeId
     * @param endNode
     * @param completeAnswerList
     */
    private static void setNodeIdToNormalAndFullAnswers(List<NodeTag> tags, Set<String> normalAnswerList,
                                                        String nodeId, boolean endNode, Set<String> completeAnswerList) {
        for (NodeTag tag : tags) {
            String code = tag.getCode();
            switch (code) {
                case COMPLETED_ANSWER:
                    //只要是完整回答额外加入到正常回答
                    completeAnswerList.add(nodeId);
                    normalAnswerList.add(nodeId);
                    break;
                case NORMAL_ANSWER:
                    //如果是正常回答，且为结束节点，额外加入到完整回答
                    if (endNode) {
                        completeAnswerList.add(nodeId);
                    }
                    normalAnswerList.add(nodeId);
                    break;
                default:
            }
        }
    }

    private VoiceConfigEntity getVoiceConfigEntity(Long speechId) {
        SpeechConfigEntity speechConfigEntity = speechConfigRepository.queryBySpeechId(speechId);
        if (Objects.isNull(speechConfigEntity)) {
            return null;
        }
        String ttsParam = speechConfigEntity.getTtsParam();
        if (StringUtils.isEmpty(ttsParam)) {
            return null;
        }
        // {"main":"tts","speed":"80","volume":"10","pitch":"0","vid":"62420"}
        JSONObject jsonObject = JSON.parseObject(ttsParam);
        String voiceId = jsonObject.getString("vid");
        Integer speed = jsonObject.getInteger("speed");
        VoiceConfigEntity voiceConfigEntity = new VoiceConfigEntity();
        voiceConfigEntity.setTtsVoiceCode(voiceId);
        voiceConfigEntity.setTtsRate(speed);
        return voiceConfigEntity;
    }

    // {"type":"String","name":"变量3","required":true,"internal":false,"remark":"","value":"顶顶顶999","max_length":300}
    private List<JSONObject> getSpeechContents(SpeechBaseEntity speechBaseEntity, Map<String, String> variableMapping) {
        List<SpeechVariableEntity> speechVariableList = speechVariableService.getSpeechVariableList(speechBaseEntity.getId());
        return getJsonObjectList(speechVariableList, variableMapping);
    }

    public static List<JSONObject> getJsonObjectList(List<SpeechVariableEntity> speechVariableList, Map<String, String> variableMapping) {
        List<JSONObject> jsonObjectList = new ArrayList<>();
        for (int i = 0; i < speechVariableList.size(); i++) {
            SpeechVariableEntity speechVariableEntity = speechVariableList.get(i);
            String limitRule = speechVariableEntity.getLimitRule();
            JSONObject rule = JSON.parseObject(limitRule);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("content", rule.getString("value"));
            jsonObject.put("index", i + 1);
            jsonObject.put("limit", rule.getIntValue("max_length"));
            String original = variableMapping.get(rule.getString("name"));
            if (!StringUtils.isEmpty(original)) {
                original = original.replace("#{", "*").replace("}", "*");
            } else {
                original = "*" + rule.getString("name") + "*";
            }
            jsonObject.put("original", original);
            jsonObject.put("remarks", rule.getString("name"));
            jsonObject.put("requiredFlag", rule.getBoolean("required"));
            jsonObject.put("sort", 0);
            jsonObject.put("systemFlag", false);
            jsonObjectList.add(jsonObject);
        }
        return jsonObjectList;
    }

    private static String getSpeechFragmentId(FlowBaseConfig flowBaseConfig) {
        if (Objects.nonNull(flowBaseConfig)) {
            List<Node> node = flowBaseConfig.getNode();
            if (!CollectionUtils.isEmpty(node)) {
                Stream<Node> nodeStream = node.stream().filter(item -> !StringUtils.isEmpty(item.getSpeechFragmentId()));
                return String.join(",", nodeStream.map(Node::getSpeechFragmentId).collect(Collectors.toSet()));
            }
        }
        return null;
    }

    private void addPublishRecord(SpeechBaseEntity speechBaseEntity, String loginUserId) {
        SpeechPublishRecordEntity speechPublishRecordEntity = new SpeechPublishRecordEntity();
        speechPublishRecordEntity.setSpeechId(speechBaseEntity.getId());
        speechPublishRecordEntity.setSpeechName(speechBaseEntity.getSpeechName());
        speechPublishRecordEntity.setSpeechIntro(speechBaseEntity.getSpeechIntro());
        speechPublishRecordEntity.setType(1);
        speechPublishRecordEntity.setFlowFilePath(speechBaseEntity.getPublishFilePath());
        speechPublishRecordEntity.setMd5Value(speechBaseEntity.getMd5Value());
        speechPublishRecordEntity.setCreatedBy(loginUserId);
        speechPublishRecordEntity.setCreatedTime(new Date());
        speechPublishRecordEntity.setId(uidService.getUID());
        speechPublishRecordEntity.setKind(PublishRecordEnums.KindEnum.SPEECH.getCode());
        publishRecordRepository.insertPublishRecord(speechPublishRecordEntity);
    }

    public void publishOperationRecordEvent(List<SpeechBaseEntity> commitDataList, String operator, HttpServletRequest request) {
        OperationRecordEvent<SpeechBaseEntity> logEvent = new OperationRecordEvent<SpeechBaseEntity>();
        logEvent.setTableName(OperationRecordConstants.SPEECH_BASE_TABLE_NAME);
        logEvent.setCommitDataList(commitDataList);
        logEvent.setCompareFields(OperationRecordConstants.SPEECH_BASE_COMPARE_FIELD);
        logEvent.setOperator(operator);
        logEvent.setIp(request.getRemoteAddr());
        applicationEventPublisher.publishEvent(logEvent);
    }

    /**
     * 添加话术
     *
     * @param speechManagerDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertSpeech(SpeechManagerBO speechManagerDto) {
        //话术名称唯一性校验
        SpeechBaseEntity speechManagerEntity =
                speechBaseRepository.getSpeechBySpeechName(speechManagerDto.getSpeechName(), null, speechManagerDto.getSpeechType());
        if (speechManagerEntity != null) {
            throw new MedicalBusinessException("话术名称不能重复");
        }
        long speechId = uidService.getUID();
        String flowFilePath = null;
        Integer speechType = speechManagerDto.getSpeechType();
        Integer model = null;
        Integer stage = null;
        if (SpeechManagerEnums.SpeechTypeEnum.STATUS_DYNAMIC.getCode().equals(speechType)) {
            String speechIntro = speechManagerDto.getSpeechIntro();
            if (speechIntro.length() > 200) {
                throw new MedicalBusinessException("描述最多200字符");
            }
            Boolean deliveryRole = checkDeliveryRole(speechManagerDto.getCreatedBy());
            if (deliveryRole && speechManagerDto.getDeliveryLlmSwitch() == 1) {
                JSONObject jsonObject = getDefaultConfig();
                // 上传话术流
                String flowJson = JSON.toJSONString(jsonObject);
                flowFilePath = uploadFlowJson(null, speechId, flowJson, null);
                model = Constants.BIG_MODEL;
                stage = Constants.STAGE_ONE;
            }

        }
        SpeechBaseEntity entity = new SpeechBaseEntity();
        entity.setId(speechId);
        entity.setCreatedBy(speechManagerDto.getCreatedBy());
        entity.setFlowFilePath(flowFilePath);
        entity.setSpeechMode(model);
        entity.setModelStage(stage);
        BeanUtils.copyProperties(speechManagerDto, entity);
        return speechBaseRepository.insertSpeech(entity);
    }

    @NotNull
    private JSONObject getDefaultConfig() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("freeSelectFlag", false);
        jsonObject.put("portraitReplySwitch", true);
        jsonObject.put("llmAnalysisSwitch", true);
        Map<String, String> llmVersionMap = new HashMap<>();
        DictPackageVO nodeSelectDict = systemDictService.getDictByValue(Constants.AI_MODEL_NODE_SELECT);
        String nodeSelect = nodeSelectDict.getChild().get(0).getDictValue();
        llmVersionMap.put(Constants.AI_MODEL_NODE_SELECT, nodeSelect);

        DictPackageVO dictByValue = systemDictService.getDictByValue(Constants.AI_MODEL_PLAY_CONTENT);
        String playContent = dictByValue.getChild().get(0).getDictValue();
        llmVersionMap.put(Constants.AI_MODEL_PLAY_CONTENT, playContent);

        DictPackageVO nodeGenerateDict = systemDictService.getDictByValue(Constants.AI_MODEL_NODE_GENERATE);
        String nodeGenerate = nodeGenerateDict.getChild().get(0).getDictValue();
        llmVersionMap.put(Constants.AI_MODEL_NODE_GENERATE, nodeGenerate);

        DictPackageVO llmVersionDict = systemDictService.getDictByValue(Constants.AI_MODEL_NODE_LLMANALYSIS);
        String llmAnalysis = llmVersionDict.getChild().get(0).getDictValue();
        llmVersionMap.put(Constants.AI_MODEL_NODE_LLMANALYSIS, llmAnalysis);
        jsonObject.put("llmVersionMap", llmVersionMap);
        return jsonObject;
    }

    /**
     * 修改话术
     *
     * @param speechManagerUpdateDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpeech(SpeechManagerUpdateDto speechManagerUpdateDto) {
        //获取话术的创建者
        SpeechBaseEntity managerById = speechBaseRepository.getManagerById(speechManagerUpdateDto.getId());
        if (managerById == null) {
            throw new MedicalBusinessException("话术不存在");
        }
        if (isViewSelfRole(speechManagerUpdateDto.getLoginUserId()) && !speechManagerUpdateDto.getLoginUserId().equals(managerById.getCreatedBy())) {
            throw new MedicalBusinessException("对不起，您无修改该话术权限");
        }
        //话术名称唯一性校验
        SpeechBaseEntity speechManagerEntity =
                speechBaseRepository.getSpeechBySpeechName(speechManagerUpdateDto.getSpeechName(), speechManagerUpdateDto.getId(), managerById.getSpeechType());
        if (speechManagerEntity != null && !speechManagerEntity.getId().equals(speechManagerUpdateDto.getId())) {
            throw new MedicalBusinessException("修改的话术名称已经存在");
        }
        Integer speechType = managerById.getSpeechType();
        if (SpeechManagerEnums.SpeechTypeEnum.STATUS_DYNAMIC.getCode().equals(speechType)) {
            String speechIntro = speechManagerUpdateDto.getSpeechIntro();
            if (speechIntro.length() > 200) {
                throw new MedicalBusinessException("描述最多200字符");
            }
        }
        SpeechBaseEntity entity = new SpeechBaseEntity();
        entity.setUpdatedBy(speechManagerUpdateDto.getLoginUserId());
        entity.setId(speechManagerUpdateDto.getId());
        entity.setSpeechName(speechManagerUpdateDto.getSpeechName());
        entity.setSpeechIntro(speechManagerUpdateDto.getSpeechIntro());
        entity.setUpdatedTime(new Date());
        entity.setBusinessDirection(speechManagerUpdateDto.getBusinessDirection());
        entity.setPlatCode(speechManagerUpdateDto.getPlatCode());
        entity.setSpeechScene(speechManagerUpdateDto.getSpeechScene());
        entity.setRange(speechManagerUpdateDto.getRange());
        entity.setDeliveryLlmSwitch(speechManagerUpdateDto.getDeliveryLlmSwitch());
        speechBaseRepository.updateSpeech(entity);
    }

    /**
     * 判断是否为只能查看编辑自己话术的角色
     *
     * @param loginUserId upaId
     * @return true or false
     */
    private boolean isViewSelfRole(String loginUserId) {
        if (StringUtils.isBlank(loginUserId)) {
            return false;
        }
        List<String> userRole = userOrgQueryApi.getUserRole(loginUserId);
        return userRole.stream().anyMatch(viewSelfRoleId::equals) && userRole.stream().noneMatch(speechManagerRoleId::equals);
    }

    /**
     * 删除话术
     *
     * @param loginUserId
     * @param speechId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSpeech(String loginUserId, Long speechId) {
        //话术权限判断
        SpeechBaseEntity entity = speechBaseRepository.getManagerById(speechId);
        if (entity == null) {
            throw new MedicalBusinessException("话术不存在");
        }
        if (isViewSelfRole(loginUserId)) {
            if (!loginUserId.equals(entity.getCreatedBy())) {
                throw new MedicalBusinessException("对不起，您无删除该话术权限");
            }
        }
        speechBaseRepository.deleteSpeech(loginUserId, speechId);
        speechBaseFragmentRelRepository.deleteBySpeechId(speechId, loginUserId);
        applicationEventPublisher.publishEvent(new FlowCacheEvent(this, speechId, null, STATUS_DELETE.getCode()));
        //发布到其他平台
        entity.setDeleted(1);
        publishToOtherPlatform(entity);
    }

    @Override
    public SpeechBaseEntity getManagerById(Long id) {
        SpeechBaseEntity speechBaseEntity = speechBaseRepository.getManagerById(id);
        if (Objects.nonNull(speechBaseEntity)) {
            List<SpeechBaseFragmentRelEntity> speechBaseFragmentRelEntityList = speechBaseFragmentRelRepository.listBySpeechId(id);
            if (CollectionUtils.isNotEmpty(speechBaseFragmentRelEntityList)) {
                List<Long> list = speechBaseFragmentRelEntityList.stream().map(SpeechBaseFragmentRelEntity::getSpeechFragmentId).collect(Collectors.toList());
                String commaSeparatedString = list.stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
                speechBaseEntity.setSpeechFragmentId(commaSeparatedString);
            }
            //话术场景
            Map<String, String> speechSceneMap = getSpeechSceneMap();
            //私有化平台
            Map<String, String> platCodeMap = getPlatCodeMap();
            //话术场景
            String speechScene = speechBaseEntity.getSpeechScene();
            speechBaseEntity.setSpeechSceneDesc(buildSpeechSceneDesc(speechScene, speechSceneMap));
            //业务方向
            String businessDirection = speechBaseEntity.getBusinessDirection();
            speechBaseEntity.setBusinessDirectionDesc(CommonUtil.getAppendValues(businessDirection, speechSceneMap));
            //私有化平台
            String platCode = speechBaseEntity.getPlatCode();
            speechBaseEntity.setPlatCodeDesc(CommonUtil.getAppendValues(platCode, platCodeMap));
            String range = speechBaseEntity.getRange();
            speechBaseEntity.setRangeDesc(buildSpeechSceneDesc(range, speechSceneMap));
        }
        return speechBaseEntity;
    }

    @Override
    public PageList<SpeechBaseEntity> getList(SpeechManagerListBO listBO, Page page) {
        SpeechManagerListDO listDo = SpeechManagerConvertor.toDo(listBO);
        if (!isViewSelfRole(listBO.getLoginUserId())) {
            listDo.setCreator(null);
        }
        Boolean deliveryRole = deliveryRole(listBO.getLoginUserId());
        setUserIdAndPlatCode(listBO, listDo, deliveryRole);
        PageList<SpeechBaseEntity> speechBaseEntityPageList = speechBaseRepository.getManagerList(listDo, page);
        if (CollectionUtils.isNotEmpty(speechBaseEntityPageList.getData())) {
            Map<String, String> uapUserMap = getUapUserMap(speechBaseEntityPageList.getData());
            Map<String, String> speechFramentNameMap = getSpeechFramentNameMap(speechBaseEntityPageList, listBO.getSpeechType());
            Map<String, Boolean> deliveryRoleMap = getDeliveryRoleMap(deliveryRole, speechBaseEntityPageList);
            //话术场景
            Map<String, String> speechSceneMap = getSpeechSceneMap();
            //私有化平台
            Map<String, String> platCodeMap = getPlatCodeMap();
            speechBaseEntityPageList.getData().forEach(speechBaseEntity -> {
                String createdBy = speechBaseEntity.getCreatedBy();
                speechBaseEntity.setDeliveryRoleSelfCreateMark(deliveryRoleMap.get(createdBy));
                speechBaseEntity.setCreatedBy(uapUserMap.get(createdBy));
                speechBaseEntity.setUpdatedBy(uapUserMap.get(speechBaseEntity.getUpdatedBy()));
                speechBaseEntity.setPublishBy(uapUserMap.get(speechBaseEntity.getPublishBy()));
                String speechFragmentName = getFragmentName(speechBaseEntity, speechFramentNameMap);
                speechBaseEntity.setSpeechFragmentName(speechFragmentName);
                // todo 业务方向，话术场景，所属平台 参考
                //话术场景
                String speechScene = speechBaseEntity.getSpeechScene();
                speechBaseEntity.setSpeechSceneDesc(buildSpeechSceneDesc(speechScene, speechSceneMap));
                //业务方向
                String businessDirection = speechBaseEntity.getBusinessDirection();
                speechBaseEntity.setBusinessDirectionDesc(CommonUtil.getAppendValues(businessDirection, speechSceneMap));
                //私有化平台
                String platCode = speechBaseEntity.getPlatCode();
                speechBaseEntity.setPlatCodeDesc(CommonUtil.getAppendValues(platCode, platCodeMap));
                String range = speechBaseEntity.getRange();
                speechBaseEntity.setRangeDesc(buildSpeechSceneDesc(range, speechSceneMap));
            });
        }
        return speechBaseEntityPageList;
    }

    @NotNull
    private Map<String, String> getPlatCodeMap() {
        Map<String, String> platCodeMap = new HashMap<>();
        List<PlatFormListVO> platFormList = nlpInterfaceService.outTransferList(new PlatFormListQueryParam());
        if (CollectionUtils.isEmpty(platFormList)) {
            return platCodeMap;
        }
        for (PlatFormListVO platFormListVO : platFormList) {
            platCodeMap.put(platFormListVO.getPlatCode(), platFormListVO.getPlatName());
        }
        return platCodeMap;
    }

    @NotNull
    private Map<String, String> getSpeechSceneMap() {
        Map<String, String> speechSceneMap = new HashMap<>();
        List<SpeechSceneConfigEntity> speechSceneConfigList = speechSceneConfigRepository.getSpeechConfigListByType(1);
        if (CollectionUtils.isEmpty(speechSceneConfigList)) {
            return speechSceneMap;
        }
        for (SpeechSceneConfigEntity speechConfig : speechSceneConfigList) {
            speechSceneMap.put(speechConfig.getId().toString(), speechConfig.getName());
        }
        return speechSceneMap;
    }

    private Map<Long, SpeechSceneConfigEntity> getSpeechSceneConfigMap() {
        Map<Long, SpeechSceneConfigEntity> speechSceneMap = new HashMap<>();
        List<SpeechSceneConfigEntity> speechSceneConfigList = speechSceneConfigRepository.getSpeechConfigListByType(1);
        if (CollectionUtils.isEmpty(speechSceneConfigList)) {
            return speechSceneMap;
        }
        for (SpeechSceneConfigEntity speechConfig : speechSceneConfigList) {
            speechSceneMap.put(speechConfig.getId(), speechConfig);
        }
        return speechSceneMap;
    }

    /**
     * 组装话术场景
     * [[1452784793452544,1452784978001920],[1452784793452544,1452785028341760],[1452784894124032,1452785070276608]]
     */
    public static String buildSpeechSceneDesc(String speechLabel, Map<String, String> speechLabelMap) {
        if (speechLabel == null || !speechLabel.contains("[")) {
            return CommonUtil.getAppendValues(speechLabel, speechLabelMap);
        }
        speechLabel = speechLabel.replace("[[", "").replace("]]", "");
        String[] pairs = speechLabel.split("\\],\\[");
        Map<String, List<String>> map = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        for (String pair : pairs) {
            String[] keyValue = pair.split(",");
            for (int j = 0; j < keyValue.length; j++) {
                stringBuilder.append(CommonUtil.getAppendValues(keyValue[j], speechLabelMap));
                if (j != keyValue.length - 1) {
                    stringBuilder.append("/");
                }
            }
            stringBuilder.append(",");
        }

        String s = stringBuilder.toString();
        if (s.endsWith(",")) {
            s = s.substring(0, s.length() - 1);
        }
        return s;
    }

    private void setUserIdAndPlatCode(SpeechManagerListBO listBO, SpeechManagerListDO listDo, Boolean deliveryRole) {
        listDo.setDeliveryRole(deliveryRole);
        UserRoleSearchParam userSearchParam = new UserRoleSearchParam();
        userSearchParam.setRoleId(deliveryRoleId);
        Page rolePage = new Page();
        rolePage.setPageIndex(1);
        rolePage.setPageSize(5000);
        PageList<UapUser> userWithRole = userOrgQueryApi.getUserWithRole(userSearchParam, listBO.getLoginUserId(), rolePage);
        List<UapUser> data = userWithRole.getData();
        List<String> userId = new ArrayList<>();
        if (!CollectionUtils.isEmpty(data)) {
            userId = data.stream().map(UapUser::getId).collect(Collectors.toList());
        }
        List<String> selfPlatCodes = new ArrayList<>();
        List<PlatCodeUserEntity> platCodeUserEntityList = platCodeUserRepository.queyPlatCodeByUserId(listBO.getLoginUserId());
        if (!CollectionUtils.isEmpty(platCodeUserEntityList)) {
            // 遍历平台编码用户实体列表，收集所有平台编码
            for (PlatCodeUserEntity platCodeUserEntity : platCodeUserEntityList) {
                String platCode = platCodeUserEntity.getPlatCode();
                // 空值检查
                if (StringUtils.isNotEmpty(platCode)) {
                    // 使用Collections.addAll避免创建中间集合
                    Collections.addAll(selfPlatCodes, platCode.split(","));
                }
            }
        }
        listDo.setUserIds(userId);
        listDo.setSelfPlatCodes(selfPlatCodes);
    }

    @NotNull
    private Map<String, Boolean> getDeliveryRoleMap(Boolean deliveryRole, PageList<SpeechBaseEntity> speechBaseEntityPageList) {
        Map<String, Boolean> deliveryRoleMap = new HashMap<>();
        if (Objects.nonNull(deliveryRole) && deliveryRole) {
            deliveryRoleMap = getDeliveryRoleMap(speechBaseEntityPageList.getData());
        }
        return deliveryRoleMap;
    }

    private Map<String, Boolean> getDeliveryRoleMap(List<SpeechBaseEntity> data) {
        Map<String, Boolean> deliveryRoleMap = new HashMap<>();
        List<String> createdByList = data.stream().map(SpeechBaseEntity::getCreatedBy).distinct().collect(Collectors.toList());
        for (String createdBy : createdByList) {
            //判断记录是否是交付管理员
            if (adminUserId.equals(createdBy)) {
                deliveryRoleMap.put(createdBy, false);
                continue;
            }
            boolean deliveryRoleCreated = userOrgQueryApi.checkUserStcRole(createdBy, deliveryRoleId);
            deliveryRoleMap.put(createdBy, deliveryRoleCreated);
        }
        return deliveryRoleMap;
    }

    /**
     * 登录人的角色是交付管理员，只能操作自己创建的话术
     *
     * @param loginUserId upaId
     * @return true or false
     */
    private Boolean deliveryRole(String loginUserId) {
        if (StringUtils.isBlank(loginUserId)) {
            return false;
        }
        boolean admin = adminUserId.equals(loginUserId);
        if (admin) {
            return false;
        }
        return userOrgQueryApi.checkUserStcRole(loginUserId, deliveryRoleId);
    }

    private static String getFragmentName(SpeechBaseEntity speechBaseEntity, Map<String, String> speechFramentNameMap) {
        if (org.springframework.util.CollectionUtils.isEmpty(speechFramentNameMap)) {
            return null;
        }
        String speechFragmentId = speechBaseEntity.getSpeechFragmentId();
        if (StringUtils.isNotEmpty(speechFragmentId)) {
            StringBuilder speechFragmentName = new StringBuilder();
            String[] split = speechFragmentId.split(",");
            for (String speechFragment : split) {
                String name = speechFramentNameMap.get(speechFragment);
                if (StringUtils.isNotEmpty(name)) {
                    speechFragmentName.append(name).append(",");
                }
            }
            if (StringUtils.isNotEmpty(speechFragmentName.toString())) {
                return speechFragmentName.substring(0, speechFragmentName.length() - 1);
            }
        }
        return null;
    }

    private Map<String, String> getSpeechFramentNameMap(PageList<SpeechBaseEntity> speechBaseEntityPageList, Integer speechType) {
        if (Objects.isNull(speechType)) {
            return null;
        }
        if (speechType.equals(SpeechManagerEnums.SpeechTypeEnum.STATUS_DYNAMIC.getCode())) {
            List<Long> speechFragmentIdList = new ArrayList<>();
            for (SpeechBaseEntity baseEntity : speechBaseEntityPageList.getData()) {
                String speechFragmentId = baseEntity.getSpeechFragmentId();
                if (StringUtils.isNotBlank(speechFragmentId)) {
                    String[] split = speechFragmentId.split(",");
                    List<Long> list = Arrays.stream(split).map(Long::valueOf).collect(Collectors.toList());
                    speechFragmentIdList.addAll(list);
                }
            }
            if (CollectionUtils.isEmpty(speechFragmentIdList)) {
                return null;
            }
            List<SpeechFragmentDO> speechFragmentList = getSpeechFragmentList(speechFragmentIdList);
            if (CollectionUtils.isEmpty(speechFragmentList)) {
                return null;
            }
            return speechFragmentList.stream().collect(Collectors.toMap(key -> String.valueOf(key.getId()), SpeechFragmentDO::getName));
        }
        return null;

    }

    private List<SpeechFragmentDO> getSpeechFragmentList(List<Long> speechFragmentIdList) {
        LambdaQueryWrapper<SpeechFragmentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SpeechFragmentDO::getId, speechFragmentIdList);
        queryWrapper.eq(SpeechFragmentDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
        return speechFragmentMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copySpeech(SpeechManagerCopyParam speechFlowCopyParam, String loginUserId) {
        Integer status = SpeechManagerEnums.SpeechPublishStatusEnum.DRAFT.getCode();
        SpeechBaseEntity speechBaseEntity = speechBaseRepository.getManagerById(speechFlowCopyParam.getSpeechId());
        Integer speechType = speechBaseEntity.getSpeechType();
        if (SpeechManagerEnums.SpeechTypeEnum.STATUS_DYNAMIC.getCode().equals(speechType)) {
            String speechIntro = speechFlowCopyParam.getSpeechIntro();
            if (speechIntro.length() > 200) {
                throw new MedicalBusinessException("描述最多200字符");
            }
            status = SpeechManagerEnums.SpeechPublishStatusEnum.STATUS_TO_RELEASED.getCode();
        }
        SpeechBaseEntity speechManagerEntity =
                speechBaseRepository.getSpeechBySpeechName(speechFlowCopyParam.getSpeechName(), null, speechType);
        if (speechManagerEntity != null) {
            throw new MedicalBusinessException("话术名称不能重复");
        }
        SpeechBaseEntity insertEntity = new SpeechBaseEntity();
        long id = uidService.getUID();
        insertEntity.setId(id);
        insertEntity.setPublishFilePath(null);
        insertEntity.setCreatedBy(loginUserId);
        insertEntity.setCreatedTime(new Date());
        insertEntity.setResourceId(speechFlowCopyParam.getSpeechId());
        insertEntity.setSpeechName(speechFlowCopyParam.getSpeechName());
        insertEntity.setVariables(speechBaseEntity.getVariables());
        insertEntity.setSpeechIntro(speechFlowCopyParam.getSpeechIntro());
        //复制话术流
        if (!StringUtils.isEmpty(speechBaseEntity.getFlowFilePath())) {
            String flowJson = downJsonFile(speechBaseEntity.getFlowFilePath());
            //交付角色复制，并且deliveryLlmSwitch是打开的，关闭自由调度开关
            Boolean deliveryRole = checkDeliveryRole(loginUserId);
            Integer deliveryLlmSwitch = speechFlowCopyParam.getDeliveryLlmSwitch();
            if (deliveryRole && Objects.nonNull(deliveryLlmSwitch) && deliveryLlmSwitch == 1) {
                JSONObject jsonObject = JSON.parseObject(flowJson, JSONObject.class);
                jsonObject.put("freeSelectFlag", false);
                flowJson = JSON.toJSONString(jsonObject);
            }
            // 上传话术流
            String flowFilePath = uploadFlowJson(null, id, flowJson, null);
            insertEntity.setFlowFilePath(flowFilePath);
        }
        insertEntity.setSpeechType(speechBaseEntity.getSpeechType());
        insertEntity.setStatus(status);
        insertEntity.setSpeechScene(speechFlowCopyParam.getSpeechScene());
        insertEntity.setBusinessDirection(speechFlowCopyParam.getBusinessDirection());
        insertEntity.setPlatCode(speechFlowCopyParam.getPlatCode());
        insertEntity.setRange(speechFlowCopyParam.getRange());
        insertEntity.setDeliveryLlmSwitch(speechFlowCopyParam.getDeliveryLlmSwitch());
        speechBaseRepository.insertSpeech(insertEntity);
        return id;
    }

    @Override
    @Async
    public void exportSpeech(SpeechManagerExportDto exportDto, long taskId, String loginUserId) {
        ExportResultBO exportResultBO = new ExportResultBO();
        exportResultBO.setTaskId(taskId);
        SpeechManagerExportBO exportBO = new SpeechManagerExportBO();
        exportBO.setIds(exportDto.getIds());
        List<SpeechBaseEntity> list = speechBaseRepository.getExportSpeech(exportBO);
        if (CollectionUtils.isEmpty(list)) {
            exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            exportResultBO.setMessage("未查询到话术信息");
            saveExportResult(exportResultBO);
            return;
        }
        try {
            //判断导出话术是否为自己的
            if (isViewSelfRole(loginUserId)) {
                for (SpeechBaseEntity speechBase : list) {
                    if (!loginUserId.equals(speechBase.getCreatedBy())) {
                        throw new MedicalBusinessException("对不起，您无导出该话术权限");
                    }
                }
            }
            Map<String, Map<String, File>> map = new HashMap<>();
            Map<String, String> uapUserMap = getUapUserMap(list);
            for (SpeechBaseEntity speechBaseEntity : list) {
                String flowJson = downJsonFile(speechBaseEntity.getFlowFilePath());
                List<SpeechBaseFragmentRelEntity> speechBaseFragmentRelEntityList = getBaseFragmentRelEntities(speechBaseEntity);
                SpeechManagerExportEntity exportEntity = SpeechManagerExportEntity.builder().id(speechBaseEntity.getId())
                        .speechName(speechBaseEntity.getSpeechName()).speechIntro(speechBaseEntity.getSpeechIntro())
                        .status(SpeechManagerEnums.SpeechPublishStatusEnum.getByCode(speechBaseEntity.getStatus()).getDesc())
                        .createdBy(uapUserMap.get(speechBaseEntity.getCreatedBy()))
                        .createdTime(new DateTime(speechBaseEntity.getCreatedTime()).toString(CommonUtil.DAY_DATE_FORMAT))
                        .flowJson(AesEncryptUtil.encrypt(flowJson))
                        .speechType(speechBaseEntity.getSpeechType())
                        .speechBaseFragmentRelEntityList(speechBaseFragmentRelEntityList)
                        .speechMode(speechBaseEntity.getSpeechMode())
                        .modelStage(speechBaseEntity.getModelStage())
                        .build();

                Map<String, File> files = new HashMap<>();
                //生成话术信息文件
                File jsonFile = File.createTempFile("temp", null);
                jsonFile.deleteOnExit();
                FileUtils.writeStringToFile(jsonFile, JacksonUtils.writeValueAsString(exportEntity), "UTF-8");
                files.put(speechBaseEntity.getSpeechName() + ".txt", jsonFile);
                //下载话术中包含的音频文件
                Map<String, File> audioFiles = downLoadAudio(flowJson);
                if (MapUtils.isNotEmpty(audioFiles)) {
                    files.putAll(audioFiles);
                }
                map.put(speechBaseEntity.getSpeechName(), files);
            }
            String url = zipFileUtils.toZipFile(map);
            if (StringUtils.isEmpty(url)) {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            } else {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.SUCCESS.getCode());
                exportResultBO.setUrl(url);
            }
        } catch (Exception e) {
            exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            exportResultBO.setMessage(e.getMessage());
        }
        saveExportResult(exportResultBO);
    }

    private List<SpeechBaseFragmentRelEntity> getBaseFragmentRelEntities(SpeechBaseEntity speechBaseEntity) {
        return speechBaseFragmentRelRepository.listBySpeechId(speechBaseEntity.getId());
    }

    @Override
    public Map<String, File> downLoadAudio(String flowJson) {
        FlowBaseConfig flowBaseConfig = JacksonUtils.readValue(flowJson, FlowBaseConfig.class);
        if (flowBaseConfig == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(flowBaseConfig.getNode())) {
            return null;
        }
        Map<String, File> files = new HashMap<>();
        for (Node node : flowBaseConfig.getNode()) {
            if (node instanceof PlayRespondNode) {
                //获取放音节点的放音内容
                List<NodePrompt> prompts = ((PlayRespondNode) node).getAllPrompts();
                if (CollectionUtils.isEmpty(prompts)) {
                    continue;
                }
                for (NodePrompt prompt : prompts) {
                    if (MapUtils.isEmpty(prompt.getAudioList())) {
                        continue;
                    }
                    //获取放音内容的音频文件
                    for (Map.Entry<String, String> entry : prompt.getAudioList().entrySet()) {
                        String audioUrl = entry.getValue();
                        if (StringUtils.isEmpty(audioUrl)) {
                            continue;
                        }
                        //如果endpoint以/结尾，去除
                        String downloadUrl;
                        if (flowConfig.getEndpoint().endsWith("/")) {
                            downloadUrl = flowConfig.getEndpoint().substring(0, flowConfig.getEndpoint().length() - 1) + audioUrl;
                        } else {
                            downloadUrl = flowConfig.getEndpoint() + audioUrl;
                        }
                        InputStream input = null;
                        try {
                            URL url = new URL(downloadUrl);
                            URLConnection connection = url.openConnection();
                            connection.setConnectTimeout(2000);
                            connection.setReadTimeout(3000);
                            input = connection.getInputStream();
                            //获取文件名
                            String fileName = audioUrl.substring(audioUrl.lastIndexOf("/") + 1);
                            //转写成文件
                            File file = File.createTempFile("temp", null);
                            file.deleteOnExit();
                            Path path = file.toPath();
                            Files.copy(input, path, StandardCopyOption.REPLACE_EXISTING);
                            files.put(fileName, file);
                        } catch (IOException e) {
                            log.error("下载文件失败", e);
                        } finally {
                            if (input != null) {
                                try {
                                    input.close();
                                } catch (IOException e) {
                                    log.error("关闭流失败", e);
                                }
                            }
                        }
                    }
                }
            }
        }
        return files;
    }

    @Override
    public void saveExportResult(ExportResultBO exportResultBO) {
        long taskId = exportResultBO.getTaskId();
        redisCacheService.setValueAndExpire(RedisCacheKey.SPEECH_EXPORT + taskId, JacksonUtils.writeValueAsString(exportResultBO), 10L,
                TimeUnit.MINUTES);
    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void importSpeech(InputStream inputStream, long taskId, String loginUserId, HttpServletRequest request) throws IOException {
        ExportResultBO exportResultBO = new ExportResultBO();
        exportResultBO.setTaskId(taskId);
        try {
            List<List<File>> speechFiles = zipFileUtils.handleFileUpload(inputStream);
            if (CollectionUtils.isEmpty(speechFiles)) {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
                saveExportResult(exportResultBO);
                return;
            }
            //批量保存到数据库
            List<SpeechBaseEntity> errorList = saveOrUpdate(speechFiles, loginUserId, request);
            if (CollectionUtils.isNotEmpty(errorList)) {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
                exportResultBO.setErrorList(errorList);

            } else {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.SUCCESS.getCode());
            }

            saveExportResult(exportResultBO);
        } catch (Exception e) {
            String message = e.getMessage();
            log.error(message);
            exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            exportResultBO.setMessage(message);
            saveExportResult(exportResultBO);
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    private List<SpeechBaseEntity> saveOrUpdate(List<List<File>> speechFiles, String loginUserId, HttpServletRequest request) throws IOException {
        List<SpeechBaseEntity> list = new ArrayList<>();
        //前置逻辑校验
        for (List<File> files : speechFiles) {
            boolean releaseFlag = false;
            SpeechBaseEntity releaseEntity = null;
            Map<String, String> audioPath = null;
            //txt文件
            File txtFile = files.stream().filter(file -> file.getName().endsWith(".txt")).findFirst().orElse(null);
            if (txtFile != null) {
                String speechStr = FileUtils.readFileToString(txtFile);
                SpeechManagerExportEntity entity = JacksonUtils.readValue(speechStr, SpeechManagerExportEntity.class);
                if (entity == null) {
                    continue;
                }
                releaseFlag = SpeechManagerEnums.SpeechPublishStatusEnum.STATUS_RELEASED.getDesc().equals(entity.getStatus());
                SpeechBaseEntity speechBaseById = speechBaseRepository.getSpeechBaseById(entity.getId());
                if (speechBaseById != null) {
                    if (isViewSelfRole(loginUserId) && !loginUserId.equals(speechBaseById.getCreatedBy())) {
                        throw new MedicalBusinessException("对不起，您无导入该话术权限");
                    }
                }
                FlowBaseConfig flowBaseConfig = JacksonUtils.readValue(AesEncryptUtil.desEncrypt(entity.getFlowJson()), FlowBaseConfig.class);
                if (null == flowBaseConfig) {
                    continue;
                }
                //获取音频文件路径并获取替换内容
                Map<String, String> repalceMap = new HashMap<>();
                audioPath = getAudioPath(flowBaseConfig, repalceMap);
                //上传流程图
                String flowJsonStr = AesEncryptUtil.desEncrypt(entity.getFlowJson());
                //Audio路径替换成当前环境路径
                flowJsonStr = replaceCurrentAudioPath(flowJsonStr, repalceMap);
                String flowFilePath = uploadFlowJson(null, entity.getId(), flowJsonStr,
                        speechBaseById != null ? speechBaseById.getFlowFilePath() : null);
                //获取话术片段id
                String speechFragmentId = getSpeechFragmentId(flowBaseConfig);
                if (speechBaseById == null) {
                    //新增
                    SpeechBaseEntity speechBase = SpeechBaseEntity.builder().id(entity.getId()).speechName(entity.getSpeechName())
                            .speechIntro(entity.getSpeechIntro())
                            .status(SpeechManagerEnums.SpeechPublishStatusEnum.getByDesc(entity.getStatus()).getCode())
                            .flowFilePath(flowFilePath).createdBy(loginUserId).createdTime(new Date())
                            .speechFragmentId(speechFragmentId).speechType(entity.getSpeechType())
                            .speechType(entity.getSpeechType())
                            .speechMode(entity.getSpeechMode())
                            .modelStage(entity.getModelStage())
                            .build();
                    speechBaseRepository.insertSpeech(speechBase);
                    releaseEntity = speechBase;
                } else {
                    //更新
                    speechBaseById.setUpdatedBy(loginUserId);
                    speechBaseById.setUpdatedTime(new Date());
                    speechBaseById.setDeleted(SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
                    speechBaseById.setStatus(SpeechManagerEnums.SpeechPublishStatusEnum.getByDesc(entity.getStatus()).getCode());
                    speechBaseById.setFlowFilePath(flowFilePath);
                    speechBaseById.setSpeechName(entity.getSpeechName());
                    speechBaseById.setSpeechIntro(entity.getSpeechIntro());
                    speechBaseById.setSpeechFragmentId(speechFragmentId);
                    speechBaseById.setSpeechMode(entity.getSpeechMode());
                    speechBaseById.setModelStage(entity.getModelStage());
                    speechBaseRepository.updateSpeech(speechBaseById);
                    releaseEntity = speechBaseById;
                }
                List<SpeechBaseFragmentRelEntity> speechBaseFragmentRelEntityList = entity.getSpeechBaseFragmentRelEntityList();
                if (CollectionUtils.isNotEmpty(speechBaseFragmentRelEntityList)) {
                    speechBaseFragmentRelRepository.deleteAndInsert(speechBaseFragmentRelEntityList);
                }
                boolean delete = txtFile.delete();
                if (!delete) {
                    log.error("删除文件失败:fileName:{}", txtFile.getName());
                }
            }
            if (releaseFlag && releaseEntity != null) {
                releaseFlow(releaseEntity, loginUserId, request);
                //如果已经同步过，不需要再次同步
                Object noNeedSync = request.getAttribute(Constants.NO_NEED_SYNC);
                if (Objects.nonNull(noNeedSync) && (Boolean) noNeedSync) {
                    log.info("SpeechFlowManagerServiceImpl[saveOrUpdate] no need sync releaseEntity:{}", JSON.toJSONString(releaseEntity));
                } else {
                    //话术发布到另一个能力平台
                    publishToOtherPlatform(releaseEntity);
                }
            }
            if (MapUtils.isEmpty(audioPath)) {
                continue;
            }
            //wav文件
            List<File> audioFiles = files.stream().filter(file -> file.getName().endsWith(".wav")).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(audioFiles)) {
                continue;
            }
            for (File file : audioFiles) {
                String audioFilePath = audioPath.get(file.getName());
                if (StringUtils.isEmpty(audioFilePath)) {
                    log.error("导入zip音频未找到上传路径：fileName:{}", file.getName());
                    continue;
                }
                audioFilePath = StringUtils.substringAfterLast(audioFilePath, pathPackage + "/");
                try (InputStream inputStream = Files.newInputStream(file.toPath())) {
                    fileUploadUtils.uploadFile(inputStream, audioFilePath);
                } catch (Exception e) {
                    log.error("导入zip音频上传失败，fileName:{}", file.getName());
                }
                boolean delete = file.delete();
                if (!delete) {
                    log.error("删除文件失败:fileName:{}", file.getName());
                }
            }
        }
        return list;
    }

    private String replaceCurrentAudioPath(String flowJsonStr, Map<String, String> repalceMap) {
        if (repalceMap.isEmpty()) {
            return flowJsonStr;
        }
        for (Map.Entry<String, String> entry : repalceMap.entrySet()) {
            flowJsonStr = flowJsonStr.replace(entry.getKey(), entry.getValue());
        }
        return flowJsonStr;
    }

    @Override
    public Map<String, String> getAudioPath(FlowBaseConfig flowBaseConfig, Map<String, String> replaceMap) {
        if (CollectionUtils.isEmpty(flowBaseConfig.getNode())) {
            return null;
        }
        //获取放音节点
        List<Node> playRespondNodeList = flowBaseConfig.getNode();
        if (CollectionUtils.isEmpty(playRespondNodeList)) {
            return null;
        }
        Map<String, String> audioMap = new HashMap<>();
        for (Node node : playRespondNodeList) {
            List<NodePrompt> prompts = ((PlayRespondNode) node).getAllPrompts();
            if (CollectionUtils.isEmpty(prompts)) {
                continue;
            }
            for (NodePrompt prompt : prompts) {
                if (MapUtils.isEmpty(prompt.getAudioList())) {
                    continue;
                }
                //获取放音内容的音频文件
                for (Map.Entry<String, String> entry : prompt.getAudioList().entrySet()) {
                    if (StringUtils.isNotBlank(entry.getValue())) {
                        String sourceFilePath = entry.getValue();
                        String fileName = sourceFilePath.substring(entry.getValue().lastIndexOf("/") + 1);
                        int suffix = sourceFilePath.indexOf("file");
                        String transferFilePath = "/" + s3Properties.getBucketName() + "/" + pathPackage + "/" + sourceFilePath.substring(suffix);
                        replaceMap.put(sourceFilePath, transferFilePath);
                        audioMap.put(fileName, transferFilePath);
                    }
                }
            }
        }
        return audioMap;
    }

    @Override
    public ExportResultBO getExportStatus(Long taskId) {
        String cacheValue = redisCacheService.getCacheValue(RedisCacheKey.SPEECH_EXPORT + taskId);
        if (StringUtils.isEmpty(cacheValue)) {
            return new ExportResultBO();
        }
        return JacksonUtils.readValue(cacheValue, ExportResultBO.class);
    }

    @Override
    public void syncExportSpeech(ExportResultBO exportResultBO, Long speechId) throws IOException {
        SpeechBaseEntity speechBaseEntity = speechBaseRepository.getSpeechBaseById(speechId);
        if (speechBaseEntity == null) {
            exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            exportResultBO.setMessage("未查询到话术信息");
            saveExportResult(exportResultBO);
            return;
        }
        String flowJson = downJsonFile(speechBaseEntity.getFlowFilePath());
        SpeechManagerExportEntity exportEntity = SpeechManagerExportEntity.builder().id(speechBaseEntity.getId())
                .speechName(speechBaseEntity.getSpeechName()).speechIntro(speechBaseEntity.getSpeechIntro())
                .status(SpeechManagerEnums.SpeechPublishStatusEnum.getByCode(speechBaseEntity.getStatus()).getDesc())
                .createdBy(speechBaseEntity.getCreatedBy())
                .createdTime(new DateTime(speechBaseEntity.getCreatedTime()).toString(CommonUtil.DAY_DATE_FORMAT))
                .flowJson(AesEncryptUtil.encrypt(flowJson)).build();

        Map<String, Map<String, File>> map = new HashMap<>();
        Map<String, File> files = new HashMap<>();
        //生成话术信息文件
        File jsonFile = File.createTempFile("temp", null);
        jsonFile.deleteOnExit();
        FileUtils.writeStringToFile(jsonFile, JacksonUtils.writeValueAsString(exportEntity), "UTF-8");
        files.put(speechBaseEntity.getSpeechName() + ".txt", jsonFile);
        //下载话术中包含的音频文件
        Map<String, File> audioFiles = downLoadAudio(flowJson);
        if (MapUtils.isNotEmpty(audioFiles)) {
            files.putAll(audioFiles);
        }
        map.put(speechBaseEntity.getSpeechName(), files);
        String url = zipFileUtils.toZipFile(map);
        exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.SUCCESS.getCode());
        exportResultBO.setUrl(url);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncImportSpeech(ExportResultBO exportResultBO, MultipartFile file, String loginUserId, HttpServletRequest request) {
        try {
            List<List<File>> speechFiles = zipFileUtils.handleFileUpload(file.getInputStream());
            if (CollectionUtils.isEmpty(speechFiles)) {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
                return;
            }
            //批量保存到数据库
            List<SpeechBaseEntity> errorList = saveOrUpdate(speechFiles, loginUserId, request);
            if (CollectionUtils.isNotEmpty(errorList)) {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
                exportResultBO.setErrorList(errorList);

            } else {
                exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.SUCCESS.getCode());
            }
        } catch (Exception e) {
            String message = e.getMessage();
            log.error(message);
            exportResultBO.setCode(SpeechManagerEnums.ExportResultEnum.FAIL.getCode());
            exportResultBO.setMessage(message);
        }
    }

    @Override
    public void lockSpeechById(Long speechId, String loginUserId) throws InterruptedException {
        RLock rLock = null;
        try {
            rLock = redissonService.getLock(RedisCacheKey.SPEECH_LOCK_PREFIX + speechId);
            rLock.tryLock(30, 300, TimeUnit.SECONDS);
            SpeechBaseEntity speechBaseEntity = speechBaseRepository.getSpeechBaseById(speechId);
            Map<String, String> uapUserMap = getUapUserMap(Collections.singletonList(speechBaseEntity));
            //无锁或重入
            if (StringUtils.isBlank(speechBaseEntity.getLockAccount()) || speechBaseEntity.getLockAccount().equals(loginUserId)) {
                speechBaseEntity.setLockAccount(loginUserId);
                speechBaseRepository.updateSpeech(speechBaseEntity);
            } else {
                throw new MedicalBusinessException("该话术已被 " + uapUserMap.get(speechBaseEntity.getLockAccount()) + " 锁定");
            }
        } catch (Exception e) {
            if (e instanceof MedicalBusinessException) {
                throw e;
            }
            log.error("话术加锁操作异常speechId: " + speechId, e);
            throw new MedicalBusinessException("该话术正在被其他人锁定中，请稍后再试");
        } finally {
            if (rLock != null) {
                try {
                    rLock.unlock();
                } catch (Exception e) {
                    log.error("{}-话术加锁操作,释放锁失败", speechId, e);
                }
            }
        }
    }

    @Override
    public void unlockSpeechById(Long speechId, Integer type, String loginUserId) {
        SpeechBaseEntity speechBaseEntity = speechBaseRepository.getSpeechBaseById(speechId);
        if (type == 1) {
            //管理员，解除任意锁
            speechBaseEntity.setLockAccount(null);
            speechBaseRepository.updateSpeech(speechBaseEntity);
        }
        if (type == 2) {
            //自己加的锁才能解锁
            if (StringUtils.isNotBlank(speechBaseEntity.getLockAccount()) && speechBaseEntity.getLockAccount().equals(loginUserId)) {
                speechBaseEntity.setLockAccount(null);
                speechBaseRepository.updateSpeech(speechBaseEntity);
            }
        }
    }

    private Map<String, String> getUapUserMap(List<SpeechBaseEntity> data) {
        Map<String, String> map = new HashMap<>();
        Set<String> collectSet = new HashSet<>();
        for (SpeechBaseEntity datum : data) {
            collectSet.add(datum.getCreatedBy());
            collectSet.add(datum.getUpdatedBy());
            collectSet.add(datum.getLockAccount());
            String publishBy = datum.getPublishBy();
            if (StringUtils.isNotBlank(publishBy)) {
                collectSet.add(publishBy);
            }
        }
        List<UapUser> uapUserList = userOrgQueryApi.getUsersByUserIds(Lists.newArrayList(collectSet), null);
        for (UapUser uapUser : uapUserList) {
            map.put(uapUser.getId(), uapUser.getName());
        }
        return map;
    }
}
