package com.iflytek.aicc.flow.infrastructure.repository.conveter;

import com.iflytek.aicc.flow.domain.manage.entity.SpeechVoiceEntity;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechVoiceDO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/8/18 10:51
 * @Description
 */
public class SpeechVoiceConvertor {
    public static SpeechVoiceEntity toEntity(SpeechVoiceDO speechVoiceDO) {
        if (speechVoiceDO == null) {
            return null;
        }
        SpeechVoiceEntity speechVoiceEntity = new SpeechVoiceEntity();
        speechVoiceEntity.setId(speechVoiceDO.getId());
        speechVoiceEntity.setSpeechId(speechVoiceDO.getSpeechId());
        speechVoiceEntity.setNodeId(speechVoiceDO.getNodeId());
        speechVoiceEntity.setName(speechVoiceDO.getName());
        speechVoiceEntity.setVoiceContent(speechVoiceDO.getVoiceContent());
        speechVoiceEntity.setVoiceUrl(speechVoiceDO.getVoiceUrl());
        speechVoiceEntity.setDeleted(speechVoiceDO.getDeleted());
        speechVoiceEntity.setTenantId(speechVoiceDO.getTenantId());
        speechVoiceEntity.setRevision(speechVoiceDO.getRevision());
        speechVoiceEntity.setCreatedBy(speechVoiceDO.getCreatedBy());
        speechVoiceEntity.setCreatedTime(speechVoiceDO.getCreatedTime());
        speechVoiceEntity.setUpdatedBy(speechVoiceDO.getUpdatedBy());
        speechVoiceEntity.setUpdatedTime(speechVoiceDO.getUpdatedTime());
        return speechVoiceEntity;
    }

    public static SpeechVoiceDO toDO(SpeechVoiceEntity speechVoiceEntity) {
        if (speechVoiceEntity == null) {
            return null;
        }
        SpeechVoiceDO speechVoiceDO = new SpeechVoiceDO();
        speechVoiceDO.setId(speechVoiceEntity.getId());
        speechVoiceDO.setSpeechId(speechVoiceEntity.getSpeechId());
        speechVoiceDO.setNodeId(speechVoiceEntity.getNodeId());
        speechVoiceDO.setName(speechVoiceEntity.getName());
        speechVoiceDO.setVoiceContent(speechVoiceEntity.getVoiceContent());
        speechVoiceDO.setVoiceUrl(speechVoiceEntity.getVoiceUrl());
        speechVoiceDO.setDeleted(speechVoiceEntity.getDeleted());
        speechVoiceDO.setTenantId(speechVoiceEntity.getTenantId());
        speechVoiceDO.setRevision(speechVoiceEntity.getRevision());
        speechVoiceDO.setCreatedBy(speechVoiceEntity.getCreatedBy());
        speechVoiceDO.setCreatedTime(speechVoiceEntity.getCreatedTime());
        speechVoiceDO.setUpdatedBy(speechVoiceEntity.getUpdatedBy());
        speechVoiceDO.setUpdatedTime(speechVoiceEntity.getUpdatedTime());
        return speechVoiceDO;
    }

    public static List<SpeechVoiceDO> toDoList(List<SpeechVoiceEntity> speechVoiceEntityList) {
        if (CollectionUtils.isEmpty(speechVoiceEntityList)) {
            return null;
        }
        return speechVoiceEntityList.stream().map(SpeechVoiceConvertor::toDO).collect(Collectors.toList());
    }
}
