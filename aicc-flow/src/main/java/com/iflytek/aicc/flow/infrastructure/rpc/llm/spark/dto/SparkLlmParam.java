package com.iflytek.aicc.flow.infrastructure.rpc.llm.spark.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @date 2024/1/9
 */
@Data
public class SparkLlmParam {
    private Head header;

    /**
     * talk（会话模式） delete 结束
     */
    @NotBlank
    private String action;
    /**
     * 会话ID
     */
    @NotBlank
    private String userId;
    /**
     * 报告id
     */
    private String reportId;
    /**
     * 报告结论
     */
    private String reportResult;
    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 背景信息
     */
    private List<Background> background;

    /**
     * 历史引用模式 0-平台兜底 1-平台不兜底
     */
    private Integer historyReferenceMode;

    /**
     * 请求内容
     */
    private InputData data;
    private List<History> history;

    @Data
    public static class History {

        private String role;

        private String content;
    }
    @Data
    public static class InputData {

        private Question question;

        @Data
        public static class Question {
            /**
             * 枚举
             *
             * 用户输入: userText
             *
             * ocr识别结果：ocrResult
             *
             * 默认: userText
             */
            private String inputType;
            /**
             * 输入的具体场景
             */
            private String inputScene;
            /**
             * 会话保留在某个场景标识
             */
            private String promptScenes;
            /**
             * 提交内容
             */
            private String text;
            /**
             * 本次问答消息编号
             */
            private String questionId;
            /**
             * 第一次提问不传，后续提问都传
             */
            private String lastquestionId;
            /**
             * 文件类型base64编码
             */
            private String fileBase64;

            private String sign;
        }
    }

    @Data
    public static class Head {

        private String aiModelVersion;

        //接入平台
        private String platform;
        /**
         * 0-医疗全链路  1 -仅gpt调用
         * 接入模式
         */
        @NotBlank
        private Integer usageMode = 1;
        /**
         * true -开启通知，false 不开启通知
         *
         * 开启通知，可以接收到当前处理的状态的消息
         */
        private Boolean notice;
        /**
         * 0-全流程调试，1-只调试不占用gpt资源
         */
        private Boolean debug;
        /**
         * 医疗大模型 ：0.1 固定答案 默认 1 随即率最大【0.1，1】
         *
         * 星火：（0，1】 1是固定，0.x是随机
         *
         * 默认值0.5
         */
        private Float temperature;
        /**
         * [0,10] 默认值 0.1
         */
        private Float topP;

        /**
         * 从k个中随机选择一个 (非等概率)
         */
        private Integer topK;
        /**
         *每次回答token数 最小值:1, 最大值:10
         */
        private Integer chunkSize;
    }

    @Data
    public static class Background {
        /**
         * personas 患者画像
         *
         * address 位置信息
         */
        private String type;
        /**
         * base64加密
         *
         * 当type=personas data为
         *
         * {
         *     "personas ": [
         *         {
         *             "role": "父亲",
         *             "basicInformation": {},
         *             "currentDesc": {},
         *             "treatment": {}
         *         }
         *     ]
         * }
         *
         * 当type=address data为
         *
         * {
         *   "province": "", //省
         *   "city": "",     //市
         *   "district": ""  //区
         * }
         */
        private String data;
    }
}
