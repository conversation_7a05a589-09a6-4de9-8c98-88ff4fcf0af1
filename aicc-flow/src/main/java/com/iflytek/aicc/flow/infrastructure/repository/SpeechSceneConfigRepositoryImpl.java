package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechSceneConfigEntity;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechSceneConfigRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.SpeechSceneConfigConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechSceneConfigDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechSceneConfigMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: zhao heng
 * @date: 2025-06-19 13:33
 * @description:
 */
@Component
public class SpeechSceneConfigRepositoryImpl implements SpeechSceneConfigRepository {

    @Autowired
    private SpeechSceneConfigMapper speechSceneConfigMapper;

    @Override
    public List<SpeechSceneConfigEntity> getSpeechConfigList(Long parentId) {
        List<SpeechSceneConfigDO> sceneConfigDOList = speechSceneConfigMapper.getSpeechConfigList(parentId);
        if (CollectionUtils.isEmpty(sceneConfigDOList)) {
            return null;
        }
        List<SpeechSceneConfigEntity> sceneConfigEntities = new ArrayList<>();
        for (SpeechSceneConfigDO speechSceneConfigDO : sceneConfigDOList) {
            SpeechSceneConfigEntity entity = SpeechSceneConfigConvertor.toEntity(speechSceneConfigDO);
            sceneConfigEntities.add(entity);
        }
        return sceneConfigEntities;
    }

    @Override
    public List<SpeechSceneConfigEntity> getSpeechConfigListByType(Integer type) {
        List<SpeechSceneConfigDO> sceneConfigDOList = speechSceneConfigMapper.getSpeechConfigListByType(type);
        if (CollectionUtils.isEmpty(sceneConfigDOList)) {
            return null;
        }
        List<SpeechSceneConfigEntity> sceneConfigEntities = new ArrayList<>();
        for (SpeechSceneConfigDO speechSceneConfigDO : sceneConfigDOList) {
            SpeechSceneConfigEntity entity = SpeechSceneConfigConvertor.toEntity(speechSceneConfigDO);
            sceneConfigEntities.add(entity);
        }
        return sceneConfigEntities;
    }

    @Override
    public int checkNameRepeat(String name, String value, Long id, Long parentId) {
        return speechSceneConfigMapper.checkNameRepeat(name, value, id, parentId);
    }

    @Override
    public void insert(SpeechSceneConfigEntity config) {
        SpeechSceneConfigDO speechSceneConfigDO = SpeechSceneConfigConvertor.toDO(config);
        speechSceneConfigMapper.insert(speechSceneConfigDO);
    }

    @Override
    public void update(SpeechSceneConfigEntity config) {
        LambdaUpdateWrapper<SpeechSceneConfigDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SpeechSceneConfigDO::getId, config.getId());
        updateWrapper.set(SpeechSceneConfigDO::getName, config.getName());
        updateWrapper.set(SpeechSceneConfigDO::getValue, config.getValue());
        updateWrapper.set(SpeechSceneConfigDO::getOperationId, config.getOperationId());
        updateWrapper.set(SpeechSceneConfigDO::getOperationType, config.getOperationType());
        updateWrapper.set(SpeechSceneConfigDO::getOperationParentId, config.getOperationParentId());
        updateWrapper.set(SpeechSceneConfigDO::getOperationName, config.getOperationName());
        updateWrapper.set(SpeechSceneConfigDO::getOperationValue, config.getOperationValue());
        updateWrapper.set(SpeechSceneConfigDO::getUpdater, config.getUpdater());
        updateWrapper.set(SpeechSceneConfigDO::getUpdateTime, config.getUpdateTime());
        speechSceneConfigMapper.update(null, updateWrapper);
    }

    @Override
    public SpeechSceneConfigEntity getById(Long id) {
        SpeechSceneConfigDO speechSceneConfigDO = speechSceneConfigMapper.selectById(id);
        return SpeechSceneConfigConvertor.toEntity(speechSceneConfigDO);
    }

    @Override
    public Boolean batchSaveSpeechConfig(List<SpeechSceneConfigEntity> configList) {
        List<SpeechSceneConfigDO> speechSceneConfigList = new ArrayList<>();
        for (SpeechSceneConfigEntity speechSceneConfigEntity : configList) {
            SpeechSceneConfigDO speechSceneConfigDO = SpeechSceneConfigConvertor.toDO(speechSceneConfigEntity);
            speechSceneConfigList.add(speechSceneConfigDO);

        }
        int row = speechSceneConfigMapper.batchInsert(speechSceneConfigList);
        return row > 0;
    }
}
