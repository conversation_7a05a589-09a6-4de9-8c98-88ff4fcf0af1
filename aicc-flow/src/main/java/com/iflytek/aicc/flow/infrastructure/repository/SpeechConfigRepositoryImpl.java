package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechConfigEntity;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechConfigRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.SpeechConfigConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechConfigDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechConfigMapper;
import org.springframework.stereotype.Service;

/**
 * 话术配置
 *
 * <AUTHOR>
 * @date 2023-08-03
 **/
@Service
public class SpeechConfigRepositoryImpl implements SpeechConfigRepository {

    private final SpeechConfigMapper speechConfigMapper;

    public SpeechConfigRepositoryImpl(SpeechConfigMapper speechConfigMapper) {
        this.speechConfigMapper = speechConfigMapper;
    }


    @Override
    public void deleteAndSaveBySpeechId(Long speechId, SpeechConfigEntity speechConfigEntity) {
        //保存之前根据话术ID删除
        LambdaUpdateWrapper<SpeechConfigDO> wrapper =
                new LambdaUpdateWrapper<SpeechConfigDO>().eq(SpeechConfigDO::getSpeechId, speechId);
        speechConfigMapper.delete(wrapper);

        SpeechConfigDO speechConfigDO = SpeechConfigConvertor.toDo(speechConfigEntity);
        speechConfigMapper.insert(speechConfigDO);
    }

    @Override
    public SpeechConfigEntity queryBySpeechId(Long speechId) {
        LambdaQueryWrapper<SpeechConfigDO> wrapper = new LambdaQueryWrapper<SpeechConfigDO>().eq(SpeechConfigDO::getSpeechId, speechId);
        SpeechConfigDO speechConfigDO = speechConfigMapper.selectOne(wrapper);
        return SpeechConfigConvertor.toEntity(speechConfigDO);
    }
}
