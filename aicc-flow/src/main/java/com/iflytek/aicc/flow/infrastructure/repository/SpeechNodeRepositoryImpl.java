package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.iflytek.aicc.flow.api.vo.SpeechNodeInterfaceVO;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechNodeEntity;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechNodeRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.SpeechNodeConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechNodeDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechNodeMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 描述：测试
 * <AUTHOR>
 * @date 2023/10/26 20:48
 */
@Service
public class SpeechNodeRepositoryImpl implements SpeechNodeRepository {
    private final SpeechNodeMapper speechNodeMapper;

    public SpeechNodeRepositoryImpl(SpeechNodeMapper speechNodeMapper) {
        this.speechNodeMapper = speechNodeMapper;
    }

    @Override
    public void deleteAndSaveBySpeechId(Long speechId, List<SpeechNodeEntity> list) {
        //保存之前根据话术ID删除
        LambdaUpdateWrapper<SpeechNodeDO> wrapper =
                new LambdaUpdateWrapper<SpeechNodeDO>().eq(SpeechNodeDO::getSpeechId, speechId);
        speechNodeMapper.delete(wrapper);

        List<SpeechNodeDO> speechNodeList = SpeechNodeConvertor.toDo(list);
        speechNodeMapper.batchSave(speechNodeList);
    }

    @Override
    public List<SpeechNodeInterfaceVO> getSpeechNodeInterface(Long speechId) {
        return speechNodeMapper.getSpeechNodeInterface(speechId);
    }
}
