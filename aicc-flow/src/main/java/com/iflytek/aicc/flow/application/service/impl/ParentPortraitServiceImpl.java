package com.iflytek.aicc.flow.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.iflytek.aicc.common.application.dto.SystemConfigSyncDto;
import com.iflytek.aicc.common.application.service.AbsSystemConfigService;
import com.iflytek.aicc.common.enums.SyncTypeEnum;
import com.iflytek.aicc.flow.application.convertor.ParentPortraitConfigConvertor;
import com.iflytek.aicc.flow.application.dto.ParentPortraitConfigDto;
import com.iflytek.aicc.flow.application.dto.ParentPortraitContentDto;
import com.iflytek.aicc.flow.application.dto.ParentPortraitContentListDto;
import com.iflytek.aicc.flow.application.dto.ParentPortraitContentQueryDto;
import com.iflytek.aicc.flow.application.dto.ParentPortraitVariableDto;
import com.iflytek.aicc.flow.application.dto.ParentPortraitVariableQueryDto;
import com.iflytek.aicc.flow.application.service.ParentPortraitService;
import com.iflytek.aicc.flow.domain.manage.bo.ParentPortraitConfigBO;
import com.iflytek.aicc.flow.domain.manage.entity.PortraitConfigEntity;
import com.iflytek.aicc.flow.domain.manage.entity.PortraitContentAndVariableEntity;
import com.iflytek.aicc.flow.domain.manage.entity.PortraitContentEntity;
import com.iflytek.aicc.flow.domain.manage.entity.PortraitVariableEntity;
import com.iflytek.aicc.flow.domain.manage.repository.PortraitConfigRepository;
import com.iflytek.aicc.flow.domain.manage.repository.PortraitContentRepository;
import com.iflytek.aicc.flow.domain.manage.repository.PortraitVariableRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.PortraitConfigConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.PortraitVariableConvertor;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.UidService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: zhao heng
 * @date: 2024-09-18 11:41
 * @description:
 */
@Service
public class ParentPortraitServiceImpl extends AbsSystemConfigService implements ParentPortraitService {

    @Autowired
    private UidService uidService;
    @Autowired
    private PortraitConfigRepository portraitConfigRepository;

    @Autowired
    private PortraitContentRepository portraitContentRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private PortraitVariableRepository portraitVariableRepository;

    private static final String PORTRAIT_CONTENT = "portraitContent";
    private static final String PORTRAIT_VARIABLE = "portraitVariable";


    @Override
    public List<PortraitConfigEntity> configList(ParentPortraitConfigDto configDto) {
        ParentPortraitConfigBO parentPortraitConfigBO = ParentPortraitConfigConvertor.toBO(configDto);
        return portraitConfigRepository.configList(parentPortraitConfigBO);
    }

    @Override
    public Long upsert(String loginUserId, ParentPortraitConfigDto configDto) {
        Long id = configDto.getId();
        if (Objects.isNull(id)) {
            return insert(loginUserId, configDto);
        }
        return update(loginUserId, configDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String loginUserId, ParentPortraitConfigDto configDto) {
        portraitConfigRepository.delete(configDto.getId(), loginUserId);
        PortraitConfigEntity portraitConfigEntity = new PortraitConfigEntity();
        portraitConfigEntity.setId(configDto.getId());
        super.syncFlowConfig(new SystemConfigSyncDto(portraitConfigEntity, this.getBeanName(), loginUserId, SyncTypeEnum.DELETE));
    }

    @Override
    public List<PortraitContentEntity> contentList(ParentPortraitContentQueryDto contentQueryDto) {
        PortraitContentEntity portraitContentEntity = PortraitConfigConvertor.toEntity(contentQueryDto);
        return portraitContentRepository.contentList(portraitContentEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void contentUpsert(String loginUserId, ParentPortraitContentDto portraitContentDto) {
        //更新患者画像配置code
        String code = portraitContentDto.getCode();
        if (StringUtils.isNotEmpty(code)) {
            updatePortraitConfig(loginUserId, portraitContentDto, code);
        }
        //批量插入/更新画像内容值
        upsertContext(loginUserId, portraitContentDto);
        //批量插入/更新画像变量值
        upsertVariable(loginUserId, portraitContentDto);

    }

    private void upsertVariable(String loginUserId, ParentPortraitContentDto portraitContentDto) {
        List<ParentPortraitVariableDto> variableList = portraitContentDto.getVariableList();
        if (CollectionUtils.isEmpty(variableList)) {
            return;
        }
        List<ParentPortraitVariableDto> insertVariableList = variableList.stream().filter(item -> Objects.isNull(item.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(insertVariableList)) {
            variableInsert(loginUserId, insertVariableList, portraitContentDto.getConfigId());
        }
        List<ParentPortraitVariableDto> updateVariableList = variableList.stream().filter(item -> Objects.nonNull(item.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateVariableList)) {
            variableUpdate(loginUserId, updateVariableList);
        }
    }

    private void upsertContext(String loginUserId, ParentPortraitContentDto portraitContentDto) {
        List<ParentPortraitContentListDto> contentList = portraitContentDto.getContentList();
        List<ParentPortraitContentListDto> insert = contentList.stream().filter(item -> Objects.isNull(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insert)) {
            contentInsert(loginUserId, insert, portraitContentDto.getConfigId());
        }
        List<ParentPortraitContentListDto> update = contentList.stream().filter(item -> Objects.nonNull(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(update)) {
            contentUpdate(loginUserId, update);
        }
    }

    @Override
    public List<PortraitVariableEntity> variableList(ParentPortraitVariableQueryDto portraitVariableDto) {
        PortraitVariableEntity portraitVariableEntity = PortraitVariableConvertor.toEntity(portraitVariableDto);
        return portraitVariableRepository.variableList(portraitVariableEntity);
    }

    @Override
    public PortraitContentAndVariableEntity contentAndVariable(ParentPortraitVariableQueryDto portraitContentDto) {
        PortraitContentAndVariableEntity portraitContentAndVariable = new PortraitContentAndVariableEntity();

        ParentPortraitContentQueryDto contentQueryDto = new ParentPortraitContentQueryDto();
        contentQueryDto.setConfigId(portraitContentDto.getConfigId());
        List<PortraitContentEntity> portraitContentEntities = this.contentList(contentQueryDto);
        portraitContentAndVariable.setPortraitContentList(portraitContentEntities);

        List<PortraitVariableEntity> portraitVariableEntityList = this.variableList(portraitContentDto);
        portraitContentAndVariable.setPortraitVariableList(portraitVariableEntityList);


        return portraitContentAndVariable;
    }

    private void variableUpdate(String loginUserId, List<ParentPortraitVariableDto> update) {
        List<PortraitVariableEntity> portraitVariableEntityList = new ArrayList<>(update.size());
        for (ParentPortraitVariableDto variableDto : update) {
            PortraitVariableEntity variableEntity = getVariableEntity(loginUserId, variableDto);
            portraitVariableEntityList.add(variableEntity);
        }
        portraitVariableRepository.batchUpdate(portraitVariableEntityList);
        HashMap<String, Object> params = new HashMap<>();
        params.put(PORTRAIT_VARIABLE, portraitVariableEntityList);
        super.syncFlowConfig(new SystemConfigSyncDto(params, this.getBeanName(), loginUserId, SyncTypeEnum.BATCH_UPDATE));
    }

    @NotNull
    private static PortraitVariableEntity getVariableEntity(String loginUserId, ParentPortraitVariableDto variableDto) {
        PortraitVariableEntity variableEntity = new PortraitVariableEntity();
        variableEntity.setId(variableDto.getId());
        variableEntity.setName(variableDto.getName());
        variableEntity.setCode(variableDto.getName());
        variableEntity.setRequired(variableDto.getRequired());
        variableEntity.setLimitRule(variableDto.getLimitRule());
        variableEntity.setInternal(variableDto.getInternal());
        variableEntity.setDefaultValue(variableDto.getDefaultValue());
        variableEntity.setRemark(variableDto.getRemark());
        variableEntity.setUpdatedTime(new Date());
        variableEntity.setUpdater(loginUserId);
        variableEntity.setDeleted(variableDto.getDeleted());
        return variableEntity;
    }

    private void variableInsert(String loginUserId, List<ParentPortraitVariableDto> insert, Long configId) {
        List<PortraitVariableEntity> portraitVariableEntityList = new ArrayList<>(insert.size());
        for (ParentPortraitVariableDto parentPortraitVariableDto : insert) {
            PortraitVariableEntity portraitVariableEntity = getVariableEntity(loginUserId, configId, parentPortraitVariableDto);
            portraitVariableEntityList.add(portraitVariableEntity);
        }
        portraitVariableRepository.batchInsert(portraitVariableEntityList);
        HashMap<String, Object> params = new HashMap<>();
        params.put(PORTRAIT_VARIABLE, portraitVariableEntityList);
        super.syncFlowConfig(new SystemConfigSyncDto(params, this.getBeanName(), loginUserId, SyncTypeEnum.BATCH_INSERT));

    }

    @NotNull
    private PortraitVariableEntity getVariableEntity(String loginUserId, Long configId, ParentPortraitVariableDto parentPortraitVariableDto) {
        PortraitVariableEntity portraitVariableEntity = new PortraitVariableEntity();
        portraitVariableEntity.setId(uidService.getUID());
        portraitVariableEntity.setConfigId(configId);
        portraitVariableEntity.setName(parentPortraitVariableDto.getName());
        portraitVariableEntity.setCode(parentPortraitVariableDto.getName());
        portraitVariableEntity.setRequired(parentPortraitVariableDto.getRequired());
        portraitVariableEntity.setLimitRule(parentPortraitVariableDto.getLimitRule());
        portraitVariableEntity.setInternal(parentPortraitVariableDto.getInternal());
        portraitVariableEntity.setDefaultValue(parentPortraitVariableDto.getDefaultValue());
        portraitVariableEntity.setRemark(parentPortraitVariableDto.getRemark());
        portraitVariableEntity.setOperator(loginUserId);
        portraitVariableEntity.setCreatedTime(new Date());
        portraitVariableEntity.setDeleted(0);
        return portraitVariableEntity;
    }

    private void updatePortraitConfig(String loginUserId, ParentPortraitContentDto portraitContentDto, String code) {
        PortraitConfigEntity portraitConfigEntity = portraitConfigRepository.queryByCode(code);
        if (Objects.nonNull(portraitConfigEntity) && !portraitConfigEntity.getId().equals(portraitContentDto.getConfigId())) {
            throw new MedicalBusinessException("患者画像编码重复");
        }
        portraitConfigEntity = new PortraitConfigEntity();
        portraitConfigEntity.setId(portraitContentDto.getConfigId());
        portraitConfigEntity.setCode(code);
        portraitConfigEntity.setUpdater(loginUserId);
        portraitConfigEntity.setUpdatedTime(new Date());
        portraitConfigRepository.update(portraitConfigEntity);
        super.syncFlowConfig(new SystemConfigSyncDto(portraitConfigEntity, this.getBeanName(), loginUserId, SyncTypeEnum.UPDATE));
    }

    private void contentUpdate(String loginUserId, List<ParentPortraitContentListDto> portraitContentDto) {
        List<PortraitContentEntity> portraitContentEntityList = new ArrayList<>(portraitContentDto.size());
        for (ParentPortraitContentListDto listDto : portraitContentDto) {
            PortraitContentEntity portraitContentEntity = new PortraitContentEntity();
            portraitContentEntity.setId(listDto.getId());
            portraitContentEntity.setContent(listDto.getContent());
            portraitContentEntity.setUpdater(loginUserId);
            portraitContentEntity.setUpdatedTime(new Date());
            portraitContentEntity.setDeleted(listDto.getDeleted());
            portraitContentEntityList.add(portraitContentEntity);
        }
        portraitContentRepository.batchUpdate(portraitContentEntityList);
        HashMap<String, Object> params = new HashMap<>();
        params.put(PORTRAIT_CONTENT, portraitContentEntityList);
        super.syncFlowConfig(new SystemConfigSyncDto(params, this.getBeanName(), loginUserId, SyncTypeEnum.BATCH_UPDATE));

    }

    private static ParentPortraitConfigDto getConfigDto(ParentPortraitContentDto portraitContentDto) {
        ParentPortraitConfigDto configDto = new ParentPortraitConfigDto();
        configDto.setId(portraitContentDto.getConfigId());
        configDto.setCode(portraitContentDto.getCode());
        return configDto;
    }

    private void contentInsert(String loginUserId, List<ParentPortraitContentListDto> portraitContentDto, Long configId) {
        List<PortraitContentEntity> portraitContentEntityList = new ArrayList<>(portraitContentDto.size());
        for (ParentPortraitContentListDto listDto : portraitContentDto) {
            PortraitContentEntity portraitContentEntity = new PortraitContentEntity();
            portraitContentEntity.setId(uidService.getUID());
            portraitContentEntity.setConfigId(configId);
            portraitContentEntity.setContent(listDto.getContent());
            portraitContentEntity.setCreatedTime(new Date());
            portraitContentEntity.setOperator(loginUserId);
            portraitContentEntity.setDeleted(0);
            portraitContentEntityList.add(portraitContentEntity);
        }
        portraitContentRepository.batchInsert(portraitContentEntityList);
        HashMap<String, Object> params = new HashMap<>();
        params.put(PORTRAIT_CONTENT, portraitContentEntityList);
        super.syncFlowConfig(new SystemConfigSyncDto(params, this.getBeanName(), loginUserId, SyncTypeEnum.BATCH_INSERT));

    }

    public Long insert(String loginUserId, ParentPortraitConfigDto configDto) {
        String code = configDto.getCode();
        if (!StringUtils.isEmpty(code)) {
            PortraitConfigEntity existPortraitConfigEntity = portraitConfigRepository.queryByCode(code);
            if (Objects.nonNull(existPortraitConfigEntity)) {
                throw new MedicalBusinessException("患者画像编码重复");
            }
        }

        int row = portraitConfigRepository.checkNameRepeat(configDto.getName(), configDto.getId(), configDto.getParentId());
        if (row > 0) {
            throw new MedicalBusinessException("同级下患者画像名称重复");
        }
        PortraitConfigEntity portraitConfigEntity = PortraitConfigConvertor.dtoToEntity(configDto);
        portraitConfigEntity.setId(uidService.getUID());
        portraitConfigEntity.setCreatedTime(new Date());
        portraitConfigEntity.setOperator(loginUserId);
        portraitConfigEntity.setDeleted(0);
        portraitConfigRepository.insert(portraitConfigEntity);
        super.syncFlowConfig(new SystemConfigSyncDto(portraitConfigEntity, this.getBeanName(), loginUserId, SyncTypeEnum.INSERT));
        return portraitConfigEntity.getId();
    }

    public Long update(String loginUserId, ParentPortraitConfigDto configDto) {
        String code = configDto.getCode();
        if (!StringUtils.isEmpty(code)) {
            PortraitConfigEntity portraitConfigEntity = portraitConfigRepository.queryByCode(configDto.getCode());
            if (Objects.nonNull(portraitConfigEntity) && !portraitConfigEntity.getId().equals(configDto.getId())) {
                throw new MedicalBusinessException("患者画像编码重复");
            }
        }
        int row = portraitConfigRepository.checkNameRepeat(configDto.getName(), configDto.getId(), configDto.getParentId());
        if (row > 0) {
            throw new MedicalBusinessException("同级下患者画像名称重复");
        }
        PortraitConfigEntity portraitConfigEntity = new PortraitConfigEntity();
        portraitConfigEntity.setId(configDto.getId());
        portraitConfigEntity.setName(configDto.getName());
        portraitConfigEntity.setCode(configDto.getCode());
        portraitConfigEntity.setUpdater(loginUserId);
        portraitConfigEntity.setUpdatedTime(new Date());
        portraitConfigRepository.update(portraitConfigEntity);
        super.syncFlowConfig(new SystemConfigSyncDto(portraitConfigEntity, this.getBeanName(), loginUserId, SyncTypeEnum.UPDATE));
        return configDto.getId();
    }

    @Override
    public String getBeanName() {
        String[] beanNamesForType = applicationContext.getBeanNamesForType(this.getClass());
        return beanNamesForType[0];
    }

    @Override
    public void receiveConfig(SystemConfigSyncDto systemConfigSyncDto) {
        SyncTypeEnum syncTypeEnum = systemConfigSyncDto.getSyncTypeEnum();
        if (SyncTypeEnum.isBatch(syncTypeEnum)) {
            Map<String, Object> params = systemConfigSyncDto.getParams();
            if (MapUtils.isEmpty(params)) {
                return;
            }
            Object object = params.get(PORTRAIT_CONTENT);
            if (Objects.nonNull(object)) {
                List<PortraitContentEntity> list = JSON.parseArray(JSON.toJSONString(object), PortraitContentEntity.class);
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                if (SyncTypeEnum.isBatchInsert(syncTypeEnum.getType())) {
                    portraitContentRepository.batchInsert(list);
                } else if (SyncTypeEnum.isBatchUpdate(syncTypeEnum.getType())) {
                    portraitContentRepository.batchUpdate(list);
                }
            }
            object = params.get(PORTRAIT_VARIABLE);
            if (Objects.nonNull(object)) {
                List<PortraitVariableEntity> list = JSON.parseArray(JSON.toJSONString(object), PortraitVariableEntity.class);
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                if (SyncTypeEnum.isBatchInsert(syncTypeEnum.getType())) {
                    portraitVariableRepository.batchInsert(list);
                } else if (SyncTypeEnum.isBatchUpdate(syncTypeEnum.getType())) {
                    portraitVariableRepository.batchUpdate(list);
                }
            }
            return;
        }
        PortraitConfigEntity portraitConfigEntity = JSON.parseObject(JSON.toJSONString(systemConfigSyncDto.getConfigData()), PortraitConfigEntity.class);
        switch (syncTypeEnum) {
            case INSERT:
                portraitConfigRepository.insert(portraitConfigEntity);
                break;
            case UPDATE:
                portraitConfigRepository.update(portraitConfigEntity);
                break;
            case DELETE:
                portraitConfigRepository.delete(portraitConfigEntity.getId(), systemConfigSyncDto.getLoginUserId());
                break;
            default:
        }
    }
}
