package com.iflytek.aicc.flow.domain.manage.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/10/18 11:13
 */
@Data
@EqualsAndHashCode
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class SpeechLabelEntity {

    /**
     * 标签
     */
    private Long id;
    /**
     * 标签编码;唯一, 提供给业务标识
     */
    private String code;
    /**
     * 标签名称
     */
    private String name;
    /**
     * 标签对应值;多个用；分割
     */
    private String values;
    /**
     * 标签类型;1:节点经过  2:命中
     */
    private Integer tagType;
    /**
     * 是否删除;0:正常  1:已删除
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedTime;

}
