package com.iflytek.aicc.flow.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.PortraitContentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: <PERSON><PERSON> heng
 * @date: 2024-09-18 11:24
 * @description:
 */
@Mapper
public interface PortraitContentMapper extends BaseMapper<PortraitContentDO> {
    /**
     * 批量插入
     * @param portraitContentDOList
     */
    void batchInsert(@Param("list") List<PortraitContentDO> portraitContentDOList);

    /**
     * 批量更新
     * @param portraitContentDOList
     */
    void batchUpdate(@Param("list") List<PortraitContentDO> portraitContentDOList);
}
