package com.iflytek.aicc.flow.infrastructure.repository.conveter;

import com.iflytek.aicc.flow.domain.manage.entity.SpeechNlpInterfaceEntity;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechNlpInterfaceDO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述：话术语义接口对象转换
 * <AUTHOR>
 * @date 2023/10/26 17:10
 */
public class SpeechNlpInterfaceConvertor {

    public static List<SpeechNlpInterfaceDO> entityToDoList(List<SpeechNlpInterfaceEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map((SpeechNlpInterfaceConvertor::entityToDo)).collect(Collectors.toList());
    }

    /**
     * 描述：DO转换为Entity
     * @param entity 话术语义接口对象
     * @return  话术语义接口实体
     */
    public static SpeechNlpInterfaceDO entityToDo(SpeechNlpInterfaceEntity entity) {
        SpeechNlpInterfaceDO speechNlpInterfaceDO = new SpeechNlpInterfaceDO();
        speechNlpInterfaceDO.setTenantId(entity.getTenantId());
        speechNlpInterfaceDO.setRevision(entity.getRevision());
        speechNlpInterfaceDO.setCreatedBy(entity.getCreatedBy());
        speechNlpInterfaceDO.setCreatedTime(entity.getCreatedTime());
        speechNlpInterfaceDO.setUpdatedBy(entity.getUpdatedBy());
        speechNlpInterfaceDO.setUpdatedTime(entity.getUpdatedTime());
        speechNlpInterfaceDO.setId(entity.getId());
        speechNlpInterfaceDO.setNodeId(entity.getNodeId());
        speechNlpInterfaceDO.setSpeechId(entity.getSpeechId());
        speechNlpInterfaceDO.setName(entity.getName());
        speechNlpInterfaceDO.setNlpCode(entity.getNlpCode());
        return speechNlpInterfaceDO;
    }
}
