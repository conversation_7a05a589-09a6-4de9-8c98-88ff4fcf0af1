package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.aicc.common.infrastructure.redis.RedisCacheService;
import com.iflytek.aicc.flow.domain.manage.bo.LlmTemplateBO;
import com.iflytek.aicc.flow.domain.manage.entity.LlmTemplateEntity;
import com.iflytek.aicc.flow.domain.manage.repository.LlmTemplateRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.LlmTemplateConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.LlmTemplateDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.LlmTemplateMapper;
import com.iflytek.outbound.utils.JacksonUtils;
import com.iflytek.outbound.utils.PageUtil;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.iflytek.aicc.common.constants.RedisCacheKey.SPARK_LLM_TEMPLATE_KEY;

/**
 * <AUTHOR>
 * @Date 2023/9/12 11:10
 * @Description  引擎配置仓库实现类
 */
@Service
public class LlmTemplateRepositoryImpl implements LlmTemplateRepository {
    private final LlmTemplateMapper llmTemplateMapper;
    private final RedisCacheService redisCacheService;
    private static final String DEFAULT_LLM_TEMPLATE_KEY = "default";
    /**
     * 缓存有效期 单位天
     */
    @Value("${redis.expire.time:30}")
    private Long redisExpireTime;
    private static final Integer DEFAULT_LLM_TEMPLATE_CODE = 1;

    public LlmTemplateRepositoryImpl(LlmTemplateMapper llmTemplateMapper, RedisCacheService redisCacheService) {
        this.llmTemplateMapper = llmTemplateMapper;
        this.redisCacheService = redisCacheService;
    }


    @Override
    public LlmTemplateEntity getLlmTemplateById(Long id) {
        LlmTemplateDO llmTemplateDO;
        //如果没空则查询默认模版
        if (null == id) {
            llmTemplateDO = getDefaultLlmTemplate();
        } else {
            llmTemplateDO = redisCacheService.getCacheHashValue(SPARK_LLM_TEMPLATE_KEY, String.valueOf(id), LlmTemplateDO.class);
            if (null == llmTemplateDO) {
                llmTemplateDO = llmTemplateMapper.selectById(id);
                if (null == llmTemplateDO) {
                    return null;
                }
                redisCacheService.setCacheHashValue(SPARK_LLM_TEMPLATE_KEY, String.valueOf(id),
                        JacksonUtils.writeValueAsString(llmTemplateDO), redisExpireTime, TimeUnit.DAYS);
            }
        }
        return LlmTemplateConvertor.toEntity(llmTemplateDO);
    }

    @Override
    public LlmTemplateEntity getLlmTemplateByType(Integer templateType) {
        LlmTemplateDO llmTemplateDO = llmTemplateMapper.getLlmTemplateByType(templateType);
        return LlmTemplateConvertor.toEntity(llmTemplateDO);
    }

    @Override
    public PageList<LlmTemplateEntity> getLlmTemplateList(LlmTemplateBO llmTemplateBO, Page page) {
        LlmTemplateDO llmTemplateDO =  LlmTemplateConvertor.toDO(llmTemplateBO);
        PageList<LlmTemplateDO> pageList = PageUtil.doPage(() -> llmTemplateMapper.getLlmTemplateList(llmTemplateDO), page);
        return LlmTemplateConvertor.toPageList(pageList);
    }

    @Override
    public Integer add(LlmTemplateBO llmTemplateBO) {
        LlmTemplateDO llmTemplateDO =  LlmTemplateConvertor.toDO(llmTemplateBO);
        return llmTemplateMapper.insert(llmTemplateDO);
    }

    @Override
    public void refreshTemplateConfig() {
        redisCacheService.deleteByKey(SPARK_LLM_TEMPLATE_KEY);
        List<LlmTemplateDO> list = llmTemplateMapper.selectList(null);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(llmTemplateDO -> {
            if (DEFAULT_LLM_TEMPLATE_CODE.equals(llmTemplateDO.getDefaultFlag())) {
                redisCacheService.setCacheHashValue(SPARK_LLM_TEMPLATE_KEY, DEFAULT_LLM_TEMPLATE_KEY,
                        JacksonUtils.writeValueAsString(llmTemplateDO), redisExpireTime, TimeUnit.DAYS);
            }
            redisCacheService.setCacheHashValue(SPARK_LLM_TEMPLATE_KEY, String.valueOf(llmTemplateDO.getId()),
                    JacksonUtils.writeValueAsString(llmTemplateDO), redisExpireTime, TimeUnit.DAYS);
        });

    }

    @Override
    public void modify(LlmTemplateBO llmTemplateBO) {
        LlmTemplateDO llmTemplateDO =  LlmTemplateConvertor.toDO(llmTemplateBO);
        llmTemplateMapper.updateById(llmTemplateDO);
        llmTemplateDO = llmTemplateMapper.selectById(llmTemplateDO.getId());
        redisCacheService.setCacheHashValue(SPARK_LLM_TEMPLATE_KEY, String.valueOf(llmTemplateDO.getId()),
                JacksonUtils.writeValueAsString(llmTemplateDO), redisExpireTime, TimeUnit.DAYS);
    }

    @Override
    public int delete(String loginUserId, Long id) {
        int i = llmTemplateMapper.deleteById(id);
        redisCacheService.deleteCacheHashValue(SPARK_LLM_TEMPLATE_KEY, String.valueOf(id));
        return i;
    }

    @Override
    public LlmTemplateEntity detail(Long id) {
        LlmTemplateDO llmTemplateDO = llmTemplateMapper.selectById(id);
        return LlmTemplateConvertor.toEntity(llmTemplateDO);
    }

    @Override
    public LlmTemplateEntity queryByName(String name) {
        LlmTemplateDO llmTemplateDO = llmTemplateMapper.queryByName(name);
        return LlmTemplateConvertor.toEntity(llmTemplateDO);
    }

    private LlmTemplateDO getDefaultLlmTemplate() {
        // 先从redis查询 如果没空则从数据库查询
        LlmTemplateDO llmTemplateDO = redisCacheService.getCacheHashValue(SPARK_LLM_TEMPLATE_KEY, DEFAULT_LLM_TEMPLATE_KEY, LlmTemplateDO.class);
        if (null == llmTemplateDO) {
            // 查询默认的模版配置
            LambdaQueryWrapper<LlmTemplateDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(LlmTemplateDO::getDefaultFlag, DEFAULT_LLM_TEMPLATE_CODE);
            llmTemplateDO = llmTemplateMapper.selectOne(lambdaQueryWrapper);
            if (null == llmTemplateDO) {
                return null;
            }
            redisCacheService.setCacheHashValue(SPARK_LLM_TEMPLATE_KEY, DEFAULT_LLM_TEMPLATE_KEY,
                    JacksonUtils.writeValueAsString(llmTemplateDO), redisExpireTime, TimeUnit.DAYS);
        }
        return llmTemplateDO;
    }


}
