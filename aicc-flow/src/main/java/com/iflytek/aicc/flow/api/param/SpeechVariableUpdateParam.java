package com.iflytek.aicc.flow.api.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2023/8/15 19:20
 * @Description
 */
@Data
public class SpeechVariableUpdateParam {
    /**
     * 主键
     */
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 话术ID
     *
     */
    @ApiModelProperty("话术ID")
    @NotNull(message = "话术ID不能为空")
    private Long speechId;
    /**
     * 节点ID
     */
    @ApiModelProperty("节点ID")
    private String nodeId;
    /**
     * 变量名称描述
     */
    @ApiModelProperty("变量名称描述")
    @NotNull(message = "变量名称描述不能为空")
    private String name;
    /**
     * 变量编码
     */
    @ApiModelProperty("变量编码")
    @NotNull(message = "变量编码不能为空")
    private String code;
    /**
     * 是否必填;只验证value_type=1 的场景
     */
    @ApiModelProperty("是否必填;只验证value_type=1 的场景")
    @NotNull(message = "是否必填不能为空")
    private Integer required;
    /**
     * 变量限制;json，长度最大、最小值
     */
    @ApiModelProperty("变量限制")
    private String limitRule;
    /**
     * 变量取值来源;1:业务传值  2:过程结果取值 3:全局最新值
     */
    @ApiModelProperty("变量取值来源")
    private Integer valueType;
    @ApiModelProperty("是否是默认引擎1是0否")
    private Integer defaultEngine;
}
