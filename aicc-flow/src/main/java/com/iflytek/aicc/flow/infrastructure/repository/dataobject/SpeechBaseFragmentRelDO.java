package com.iflytek.aicc.flow.infrastructure.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: <PERSON><PERSON> he<PERSON>
 * @date: 2024-09-02 8:26
 * @description: 话术流程与话术问题关系表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_aicc_speech_base_fragment_rel")
public class SpeechBaseFragmentRelDO {

    /**
     * 主键
     */
    @ApiModelProperty(name = "主键", notes = "")
    @TableId
    private Long id;
    /**
     * 话术ID
     */
    @ApiModelProperty(name = "话术ID", notes = "")
    private Long speechId;
    /**
     * 话术片段id
     */
    @ApiModelProperty(name = "话术片段id", notes = "")
    private Long speechFragmentId;
    /**
     * 话术与片段状态 0-正常 1-待处理（片段修改了，动态话术待处理）
     */
    @ApiModelProperty(name = "话术与片段状态 0-正常 1-待处理（片段修改了，动态话术待处理）", notes = "")
    private Integer relState;
    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人", notes = "")
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间", notes = "")
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(name = "更新人", notes = "")
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(name = "更新时间", notes = "")
    private Date updatedTime;
    /**
     * 是否删除;0:正常  1:已删除
     */
    @ApiModelProperty(name = "是否删除;0:正常  1:已删除", notes = "")
    private Integer deleted;

}
