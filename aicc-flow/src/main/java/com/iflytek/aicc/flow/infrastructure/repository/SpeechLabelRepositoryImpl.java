package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.iflytek.aicc.flow.common.enums.SpeechManagerEnums;
import com.iflytek.aicc.flow.domain.manage.bo.SpeechLabelListBO;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechLabelEntity;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechLabelRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.SpeechLabelConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechLabelDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechLabelMapper;
import com.iflytek.fpva.uapcenter.pojo.UapUser;
import com.iflytek.fpva.uapcenter.service.UserOrgQueryApi;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.outbound.utils.PageUtil;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2023/9/19 9:25
 * @Description 话术标签管理
 */
@Service
public class SpeechLabelRepositoryImpl implements SpeechLabelRepository {
    private final SpeechLabelMapper speechLabelMapper;
    private final UserOrgQueryApi userOrgQueryApi;

    public SpeechLabelRepositoryImpl(SpeechLabelMapper speechLabelMapper, UserOrgQueryApi userOrgQueryApi) {
        this.speechLabelMapper = speechLabelMapper;
        this.userOrgQueryApi = userOrgQueryApi;
    }

    @Override
    public PageList<SpeechLabelEntity> query(SpeechLabelListBO speechLabelListBO, Page page) {
        LambdaQueryWrapper<SpeechLabelDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SpeechLabelDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
        lambdaQueryWrapper.eq(StringUtils.isNotEmpty(speechLabelListBO.getCode()), SpeechLabelDO::getCode, speechLabelListBO.getCode());
        lambdaQueryWrapper.eq(StringUtils.isNotEmpty(speechLabelListBO.getName()), SpeechLabelDO::getName, speechLabelListBO.getName());
        lambdaQueryWrapper.like(StringUtils.isNotEmpty(speechLabelListBO.getValues()), SpeechLabelDO::getValues, speechLabelListBO.getValues());
        lambdaQueryWrapper.eq(speechLabelListBO.getTagType() != null, SpeechLabelDO::getTagType, speechLabelListBO.getTagType());
        lambdaQueryWrapper.orderByDesc(SpeechLabelDO::getUpdatedTime).orderByDesc(SpeechLabelDO::getCreatedTime);
        PageList<SpeechLabelDO> pageListData = PageUtil.doPage(() -> speechLabelMapper.selectList(lambdaQueryWrapper), page);
        List<SpeechLabelDO> data = pageListData.getData();
        Map<String, String> map = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(data)) {
            getUapUserMap(data, map);
        }
        return SpeechLabelConvertor.toPageList(pageListData, map);
    }

    @Override
    public void add(SpeechLabelEntity speechLabelEntity) {
        String code = speechLabelEntity.getCode();
        if (StringUtils.isEmpty(code)) {
            throw new MedicalBusinessException("标签编码不能为空");
        }
        LambdaQueryWrapper<SpeechLabelDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SpeechLabelDO::getCode, code);
        SpeechLabelDO dbSpeechLabel = speechLabelMapper.selectOne(lambdaQueryWrapper);
        SpeechLabelDO speechLabelDO = SpeechLabelConvertor.toDO(speechLabelEntity);
        if (dbSpeechLabel == null) {
            //如果标签code不存在新增
            speechLabelMapper.insert(speechLabelDO);
            return;
        }

        Integer deleted = dbSpeechLabel.getDeleted();
        if (deleted != null && deleted == 0) {
            throw new MedicalBusinessException("标签编码重复");
        }
        LambdaUpdateWrapper<SpeechLabelDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SpeechLabelDO::getCode, code);
        speechLabelMapper.update(speechLabelDO, updateWrapper);

    }

    @Override
    public int delete(SpeechLabelEntity speechLabelEntity) {
        SpeechLabelDO speechLabelDO = SpeechLabelConvertor.toDO(speechLabelEntity);
        speechLabelDO.setDeleted(SpeechManagerEnums.SpeechStatusEnum.STATUS_DELETED.getCode());
        return speechLabelMapper.updateById(speechLabelDO);
    }

    @Override
    public SpeechLabelEntity getById(Long id) {
        SpeechLabelDO speechLabelDO = speechLabelMapper.selectById(id);
        return SpeechLabelConvertor.toEntity(speechLabelDO);
    }

    @Override
    public void updateById(SpeechLabelEntity speechLabelEntity) {
        String code = speechLabelEntity.getCode();
        if (StringUtils.isEmpty(code)) {
            throw new MedicalBusinessException("标签编码不能为空");
        }
        LambdaQueryWrapper<SpeechLabelDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SpeechLabelDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
        lambdaQueryWrapper.eq(SpeechLabelDO::getCode, code);
        lambdaQueryWrapper.ne(SpeechLabelDO::getId, speechLabelEntity.getId());
        Integer count = speechLabelMapper.selectCount(lambdaQueryWrapper);
        if (count > 0) {
            throw new MedicalBusinessException("标签编码重复");
        }

        SpeechLabelDO speechLabelDO = SpeechLabelConvertor.toDO(speechLabelEntity);
        speechLabelMapper.updateById(speechLabelDO);
    }

    private void getUapUserMap(List<SpeechLabelDO> data, Map<String, String> map) {
        Set<String> collectSet = new HashSet<>();
        for (SpeechLabelDO datum : data) {
            collectSet.add(datum.getCreatedBy());
            collectSet.add(datum.getUpdatedBy());
        }
        List<UapUser> uapUserList = userOrgQueryApi.getUsersByUserIds(Lists.newArrayList(collectSet), null);
        for (UapUser uapUser : uapUserList) {
            map.put(uapUser.getId(), uapUser.getName());
        }
    }
}
