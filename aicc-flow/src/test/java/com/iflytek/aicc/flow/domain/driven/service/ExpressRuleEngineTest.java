package com.iflytek.aicc.flow.domain.driven.service;

import com.iflytek.aicc.flow.common.Constants;
import com.iflytek.aicc.flow.domain.driven.engine.ExpressRuleContext;
import com.iflytek.aicc.flow.domain.driven.engine.ExpressRuleEngine;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * 方法说明
 *
 * <AUTHOR>
 * @className aicc-platform
 * @date $ $
 */
@Slf4j
public class ExpressRuleEngineTest {

    @Test
    public void commonExecute() throws Exception {
        Map<String, Object> ruleMap = new HashMap<>();
        ruleMap.put(Constants.RULE_PARAM_REQUEST, new HashMap<>());
        //中间过程生成上下文结果 todo暂时不支持上下文语义结果

        Map<String, Object> currentMap = new HashMap<>();
        currentMap.put("name", "不是本人");
        ruleMap.put(Constants.RULE_PARAM_CURRENT, currentMap);
        ruleMap.put(Constants.RULE_PARAM_SESSION, new HashMap<>());
        ruleMap.put(Constants.ASR_CONTEXT, "input");
        ExpressRuleContext context = new ExpressRuleContext(ruleMap);

        Object object = ExpressRuleEngine.getExpressRunner().execute(
                "if ( current.name == '是本人') { value = '他';} else { value = '您'; value1 = '2';} ;",
                context, null, true, false);
        Map<String, Object> result = new HashMap<>();
        context.keySet().forEach(key -> {
            if (!key.equals(Constants.RULE_PARAM_REQUEST) && !key.equals(Constants.RULE_PARAM_CURRENT) &&
                    !key.equals(Constants.RULE_PARAM_SESSION) &&
                    !key.equals(Constants.ASR_CONTEXT)) {
                result.put(key, context.get(key));
            }
        });
        log.info("new value :{}", result);
    }

    @Test
    public void test() {
        Map<String, Object> currentMap = new HashMap<>();
        Map<String, Object> req = new HashMap<>();
        Map<String, Object> ruleMap = new HashMap<>();
        currentMap.put("medicationFrequency", "5");
        currentMap.put("medicationCounts", "1");
        ruleMap.put(Constants.RULE_PARAM_REQUEST, req);
        ruleMap.put(Constants.RULE_PARAM_CURRENT, currentMap);
        ruleMap.put(Constants.RULE_PARAM_SESSION, new HashMap<>());
        ruleMap.put(Constants.ASR_CONTEXT, "input");
        ExpressRuleContext context = new ExpressRuleContext(ruleMap);
        ExpressRuleEngine.checkSyntax("!current.medicationFrequency1");
        Object object = ExpressRuleEngine.booleanExecute(
                "current.medicationFrequency <> ''",
                context);
        log.info("boolean :{}", object);
    }
}