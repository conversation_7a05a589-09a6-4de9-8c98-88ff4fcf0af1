package com.iflytek.aicc.flow.domain.driven.engine;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * 方法说明
 *
 * <AUTHOR>
 * @className aicc-platform
 * @date $ $
 */
@Slf4j
public class KeywordRuleEngineTest {

    @Test
    public void invoke() {
        String express = "((没|不)&(运动|锻炼))|((带|看|接|送)&(孩子|孙子|孙女|小孩|外甥|侄子|侄女))" +
                "|(干活|上班|做工|工作|劳动|干田|农活|种地|扫大街|清洁工|打工|搞垃圾|有活干|保洁|苦力活|体力活|干苦力" +
                "|干体力|工地|干生产|做家务|干家务|搞家务|忙家务|忙家里|搞家里|打扫卫生|扫地|拖地|做饭|买菜|做菜|烧饭|烧菜|洗衣服)";
        KeywordRuleEngine keywordRuleEngine = new KeywordRuleEngine(express);
        boolean flag = keywordRuleEngine.invoke("没运动");
        log.info("flag:{}", flag);
        assertTrue(flag);

        flag = keywordRuleEngine.invoke("不运动");
        assertTrue(flag);

        flag = keywordRuleEngine.invoke("方式孩子");
        assertFalse(flag);

        flag = keywordRuleEngine.invoke("劳动");
        assertTrue(flag);

        flag = keywordRuleEngine.invoke("做sd务");
        assertFalse(flag);

        String ex = "((不咸不淡|不行也不大|不行不大|不行|正常|不行|偏大|平淡|口腔|听那|系统|步行不大|清单|很大|" +
                "不大|不好也不大|篇担|陪伴|平淡|一百)|((不|没)&(斜|痫|偏斜|蹁跹|癫痫|谢谢|时间|闲|篇闲|有点点钱|象牙|" +
                "行天前|谁|天仙|偏稀|珍惜|衔|偏线|千元|显)))";
        keywordRuleEngine = new KeywordRuleEngine(ex);
        flag = keywordRuleEngine.invoke("不斜");
        log.info("flag:{}", flag);

        String ex1 = "((有啊|跑步|散步|三部|遛弯|溜达|广场舞|太极拳|气功|鞭子|毽子|逛公园|遛狗|溜溜|遛遛|" +
                "走路|走步|竞走|慢走|转转|跑跑|慢跑|跑步|跳绳|踢毽子|做操|体操|活动|走一走)&!((没|不)&(运动|锻炼|活动)))";
        keywordRuleEngine = new KeywordRuleEngine(ex1);
        flag = keywordRuleEngine.invoke("散步遛弯");
        log.info("flag:{}", flag);
    }
}