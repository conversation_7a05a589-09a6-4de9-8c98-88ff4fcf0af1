package com.iflytek.aicc.flow.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 音频测试工具类
 */
@Slf4j
public class AudioTestUtils {

    /**
     * 从classpath加载音频文件
     */
    public static byte[] loadAudioFromClasspath(String path) throws IOException {
        ClassPathResource resource = new ClassPathResource(path);
        try (InputStream inputStream = resource.getInputStream()) {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            byte[] data = new byte[1024];
            int bytesRead;

            while ((bytesRead = inputStream.read(data)) != -1) {
                buffer.write(data, 0, bytesRead);
            }

            return buffer.toByteArray();
        }
    }

    /**
     * 生成测试用的PCM音频数据
     */
    public static byte[] generateTestPcmAudio(int durationMs, int sampleRate) {
        int samples = (durationMs * sampleRate) / 1000;
        byte[] audioData = new byte[samples * 2]; // 16位音频，每个样本2字节

        // 生成简单的正弦波
        for (int i = 0; i < samples; i++) {
            double angle = 2.0 * Math.PI * i * 440.0 / sampleRate; // 440Hz音调
            short sample = (short) (Short.MAX_VALUE * 0.1 * Math.sin(angle));

            audioData[i * 2] = (byte) (sample & 0xFF);
            audioData[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
        }

        return audioData;
    }

    /**
     * WAV转PCM
     */
    public static byte[] wavToPcm(byte[] wavData) throws Exception {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(wavData);
             AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(bais)) {

            AudioFormat format = audioInputStream.getFormat();
            log.info("音频格式: 采样率={}, 位深={}, 声道数={}",
                    format.getSampleRate(), format.getSampleSizeInBits(), format.getChannels());

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;

            while ((bytesRead = audioInputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }

            return baos.toByteArray();
        }
    }
}
