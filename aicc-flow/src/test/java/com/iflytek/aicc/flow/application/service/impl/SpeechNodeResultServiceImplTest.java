package com.iflytek.aicc.flow.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.iflytek.aicc.flow.application.dto.FlowDialogDto;
import com.iflytek.aicc.flow.domain.driven.entity.result.SpeechNodeResultEntity;
import com.iflytek.aicc.flow.infrastructure.repository.SpeechNodeResultRepositoryImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

/**
 * 方法说明
 *
 * <AUTHOR>
 * @className aicc-platform
 * @date $ $
 */
@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class SpeechNodeResultServiceImplTest {

    // 被测类
    @InjectMocks
    private SpeechNodeResultServiceImpl speechNodeResultService;

    @Mock
    private SpeechNodeResultRepositoryImpl speechNodeResultRepository;

    @Test
    public void pushDialogByCallId() {
        Long callId = 1555897479757824L;
        Mockito.when(speechNodeResultRepository.selectSpeechNodeResultList(callId)).thenReturn(new ArrayList<SpeechNodeResultEntity>() {

            private static final long serialVersionUID = -3929627693817362886L;
                {
                    add(SpeechNodeResultEntity.builder().nodeName("自我介绍与居民确认").seqNo(0)
                            .nodeId("word_node_8e1b24ab-").speechId(1552902150635521L).
                            robotText("您好，我这边是陕西人民医院神经内科的工作人员，请问您是[r1]胡涛吗？")
                            .identifyAudioUrl(null).tags(null).identifyText(null).nlpResult(null).nlpCode(null).build());
                    add(SpeechNodeResultEntity.builder().nodeName("身体恢复情况").seqNo(1)
                            .nodeId("word_node_a2ceb601-").speechId(1552902150635521L).
                            robotText("是这样子的，您之前在我们科室住院治疗过，这次联系您呢，是想了解一下您出院后的恢复情况。请问您目前身体有没有好转啊？")
                            .identifyAudioUrl("1555897479757824_a96679aa-6654-45c4-b65e-ec516ad34d37_1.wav")
                            .tags(null).identifyText("是的").nlpResult("{\"selfIntroduction\":\"是本人\"}").nlpCode("selfIntroduction_iris1").build());
                    add(SpeechNodeResultEntity.builder().nodeName("未好转-服药依从性").seqNo(2)
                            .nodeId("word_node_5579d8b4-").speechId(1552902150635521L).
                            robotText("那您平时要注意保持良好的心态，注意劳逸结合。合理饮食、戒烟戒酒。您平时有按照医嘱服药吗？")
                            .identifyAudioUrl("1555897479757824_a96679aa-6654-45c4-b65e-ec516ad34d37_2.wav").tags(null).identifyText("没有")
                            .nlpResult("{\"physicalRecovery\":\"未好转\"}").nlpCode("physicalRecovery_iris").build());
                    add(SpeechNodeResultEntity.builder().nodeName("规律-复诊询问").seqNo(3)
                            .nodeId("word_node_58c87c5a-").speechId(1552902150635521L).
                            robotText("最后问一下，您有没有定期来医院复诊呀？")
                            .identifyAudioUrl("1555897479757824_a96679aa-6654-45c4-b65e-ec516ad34d37_3.wav")
                            .tags("COMPLETED_ANSWER,NORMAL_ANSWER").identifyText("有").nlpResult("{\"medicationCompliance\":\"规律\"}")
                            .nlpCode("medicationCompliance_iris4").build());
                    add(SpeechNodeResultEntity.builder().nodeName("结束语").seqNo(4)
                            .nodeId("word_node_c1b17861-").speechId(1552902150635521L).
                            robotText("好的，您的情况我知道了,出院后请合理饮食，按时服药，戒烟戒酒，定期复诊。 您在恢复的过程中有任何疑问，都可以拨打我们科室电话，号码是联系号码进行咨询，祝您生活愉快，再见。")
                            .identifyAudioUrl("1555897479757824_a96679aa-6654-45c4-b65e-ec516ad34d37_5.wav").tags("COMPLETED_ANSWER,NORMAL_ANSWER")
                            .identifyText("有啊").nlpResult("{\"reExaminationAslk\":\"复诊\"}")
                            .nlpCode("reExaminationAslk_iris").build());
                    add(SpeechNodeResultEntity.builder().nodeName("结束语").seqNo(5)
                            .nodeId("word_node_c1b17861-").speechId(1552902150635521L).
                            robotText("")
                            .identifyAudioUrl("").tags("COMPLETED_ANSWER,NORMAL_ANSWER").identifyText("playover").nlpResult(null)
                            .nlpCode("reExaminationAslk_iris").build());
                }
        });
        FlowDialogDto flowDialogDto = speechNodeResultService.getFlowDialogDto(callId);
        log.info("flowDialogDto:{}", JSON.toJSONString(flowDialogDto));
        Assert.assertEquals(flowDialogDto.getDialogs().size(), 5);
    }
}