package com.iflytek.aicc.flow.service;

import com.iflytek.aicc.common.infrastructure.redis.RedisCacheService;
import com.iflytek.aicc.flow.api.vo.cti.FlowResultVO;
import com.iflytek.aicc.flow.application.dto.HeartbeatDto;
import com.iflytek.aicc.flow.application.service.FlowHeartbeatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 方法说明
 *
 * <AUTHOR>
 * @className aicc-platform
 * @date $ $
 */
@Slf4j
@Service
public class FlowHeartbeatServiceTest {

    @Autowired
    public FlowHeartbeatService flowHeartbeatService;
    @Autowired
    public RedisCacheService redisCacheService;

    public FlowResultVO<?> testFlowHearbeatReceive(Long callId) {
        HeartbeatDto heartbeatParam = new HeartbeatDto();
        heartbeatParam.setCallId(String.valueOf(callId));
        return flowHeartbeatService.heartbeatReceive(heartbeatParam);
    }

    public void testDestroySessionByHeartBeat() {
        flowHeartbeatService.destroySessionByHeartBeat();
    }
}