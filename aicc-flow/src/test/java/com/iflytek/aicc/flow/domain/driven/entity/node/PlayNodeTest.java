package com.iflytek.aicc.flow.domain.driven.entity.node;

import com.iflytek.aicc.flow.application.cache.FlowSession;
import com.iflytek.aicc.flow.application.cache.ThreadSessionContext;
import com.iflytek.aicc.flow.application.dto.InvokeMessageDto;
import com.iflytek.aicc.flow.domain.driven.entity.NodeNlp;
import com.iflytek.aicc.flow.domain.driven.entity.NodePrompt;
import com.iflytek.aicc.flow.domain.driven.entity.NodeSetting;
import com.iflytek.aicc.flow.domain.driven.entity.link.ConditionLink;
import com.iflytek.aicc.flow.domain.driven.entity.link.QlExpressLink;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.iflytek.aicc.flow.common.Constants.RULE_PARAM_CURRENT;
import static com.iflytek.aicc.flow.common.enums.FlowPromptEnums.FlowPromptTypeEnum.FLOW_PROMPT_COMMON;

/**
 * 方法说明
 *
 * <AUTHOR>
 * @className aicc-platform
 * @date $ $
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class PlayNodeTest {

    // 被测类
    @InjectMocks
    private PlayRespondNode playRespondNode;

    @Mock
    private NodeNlp nodeNlp;

    @Test
    public void skip() {
    }

    @Test
    public void findNextNode() {
//        MockitoAnnotations.initMocks(this);
        //手动注入mock对象
//        NodeNlp nodeNlp = Mockito.mock(NodeNlp.class);
        FlowSession session = new FlowSession();
        Mockito.doAnswer(invocation -> {
            session.setCurrentContext(new HashMap() {
                private static final long serialVersionUID = 8504671251000172849L;

                {
                    put("introduction", "是本人");
                }
            });
            return null;
        }).when(nodeNlp).nlpCall(session, null);

        /*Mockito.when(nodeNlp.nlpCall(session)).thenReturn(new HashMap() {
            private static final long serialVersionUID = -2905539309684342537L;

            {
            put("introduction", "是本人");
        }});*/
        session.setCurrentContext(new HashMap() {
            private static final long serialVersionUID = 8504671251000172849L;

            {
                put("introduction", "是本人");
            }
        });
        NodeSetting nodeSetting = new NodeSetting();
        nodeNlp.setCode("selfIntroduction");
        nodeSetting.setNlp(nodeNlp);
        playRespondNode.setSetting(nodeSetting);

        List<NodePrompt> nodePrompts = new ArrayList<>();
        NodePrompt nodePrompt = new NodePrompt();
        nodePrompt.setInterrupt(false);
        nodePrompt.setText("你好，我是${自我介绍}的助手，请问您是${居民名称}吗？");
        nodePrompt.setType(FLOW_PROMPT_COMMON.getCode());
        nodePrompts.add(nodePrompt);
        playRespondNode.setPrompts(nodePrompts);

        QlExpressLink link = new QlExpressLink();
        link.setCondition(RULE_PARAM_CURRENT + ".introduction == '是本人'");
        link.setId("123456");
        link.setName("自我介绍判定");
        link.setPostScript("if ( current.introduction == '是本人') { value = '您';} else { value = '他';}");
        playRespondNode.setLinkers(new ArrayList<ConditionLink>() {
            private static final long serialVersionUID = -2905539309684342537L;
            {
            add(link);
        }});
        InvokeMessageDto invokeMessageDto = new InvokeMessageDto();
        invokeMessageDto.setContent("是的");
        session.setInvokeMessage(invokeMessageDto);
        session.setNodeMap(new HashMap<>());
        ThreadSessionContext.setSession(session);

        playRespondNode.initialize(false);
        playRespondNode.findNextNode();

        log.info("{}", session.getInnerContext());
        Assert.assertEquals("您", session.getInnerContext().get("value"));
    }
}