package com.iflytek.aicc.flow.domain.driven.entity;

import com.alibaba.fastjson.JSON;
import com.iflytek.aicc.flow.application.cache.FlowSession;
import com.iflytek.aicc.flow.application.cache.ThreadSessionContext;
import com.iflytek.aicc.flow.application.dto.InvokeMessageDto;
import com.iflytek.aicc.flow.domain.driven.entity.play.PlayEventMessage;
import com.iflytek.aicc.flow.domain.driven.entity.variable.BaseVariable;
import com.iflytek.aicc.flow.domain.driven.entity.variable.StringVariable;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.iflytek.aicc.flow.common.Constants.AUDIO_FIELD_NAME_REPLACE_END;
import static com.iflytek.aicc.flow.common.Constants.AUDIO_FIELD_NAME_REPLACE_START;
import static com.iflytek.aicc.flow.common.enums.FlowInvokeEventEnums.FlowEnum.BUSINESS_TYPE_TEST;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 方法说明
 *
 * <AUTHOR>
 * @className aicc-platform
 * @date $ $
 */
@Slf4j
@RunWith(SpringRunner.class)
class NodePromptTest {

    @Test
    void test() {
        String src = "${喂您好，我是}#{自我介绍}${的助手，请问您是}#{居民名称}${吗？}";
        src = src.replaceAll(AUDIO_FIELD_NAME_REPLACE_START, "")
                .replaceAll(AUDIO_FIELD_NAME_REPLACE_END, "");
        log.info(src);
    }

    @Test
    void getPlayListCondition() {
        NodePrompt nodePrompt = new NodePrompt();
        nodePrompt.setText("${喂您好，我是}#{自我介绍}${的助手，请问您是}#{居民名称}${吗？}");
        List<String> playList = nodePrompt.getPlayList();
        log.info("playList:{}", playList);
        assertEquals(5, playList.size());

        List<String> targetList = new ArrayList<>();
        targetList.add("${喂您好，我是}");
        targetList.add("#{自我介绍}");
        targetList.add("${的助手，请问您是}");
        targetList.add("#{居民名称}");
        targetList.add("${吗？}");
        assertArrayEquals(targetList.toArray(), playList.toArray());
    }

    @Test
    void getPlayListCondition2() {
        NodePrompt nodePrompt = new NodePrompt();
        nodePrompt.setText("您好，${我是}#{自我介绍}${的助手，请问您是}#{居民名称}吗？");
        List<String> playList = nodePrompt.getPlayList();
        log.info("playList:{}", playList);
        assertEquals(5, playList.size());

        List<String> targetList = new ArrayList<>();
        targetList.add("您好，");
        targetList.add("${我是}");
        targetList.add("#{自我介绍}");
        targetList.add("${的助手，请问您是}");
        targetList.add("#{居民名称}吗？");
        assertArrayEquals(targetList.toArray(), playList.toArray());
    }

    @Test
    void getPlayListConditionText() {
        NodePrompt nodePrompt = new NodePrompt();
        nodePrompt.setText("您好，#{自我介绍}，请问你是#{居民名称}吗？");
        List<String> playList = nodePrompt.getPlayList();
        log.info("playList:{}", playList);
        assertEquals(1, playList.size());

        List<String> targetList = new ArrayList<>();
        targetList.add("您好，#{自我介绍}，请问你是#{居民名称}吗？");
        assertArrayEquals(targetList.toArray(), playList.toArray());
    }

    @Test
    void getPlayListConditionVoice() {
        NodePrompt nodePrompt = new NodePrompt();
        nodePrompt.setText("${您好，我是外呼的助手，}${请问你是谁吗}");
        List<String> playList = nodePrompt.getPlayList();
        log.info("playList:{}", playList);
        assertEquals(2, playList.size());

        List<String> targetList = new ArrayList<>();
        targetList.add("${您好，我是外呼的助手，}");
        targetList.add("${请问你是谁吗}");

        assertArrayEquals(targetList.toArray(), playList.toArray());
    }

    @Test
    public void testCallwrap() {
        String src = "${喂您好，我是}#{自我介绍}${的助手，请问您是}#{居民名称}${吗？}";
        NodePrompt nodePrompt = new NodePrompt();
        nodePrompt.setText(src);

        String voice = "/outbound/voice/0.mp3";
        String voice1 = "/outbound/voice/1.mp3";
        String voice2 = "/outbound/voice/2.mp3";

        Map<String, String> audioMap = new HashMap<>();
        audioMap.put("喂您好，我是", voice);
        audioMap.put("的助手，请问您是", voice1);
        audioMap.put("吗？", voice2);
        nodePrompt.setAudioList(audioMap);

        FlowSession session = new FlowSession();
        Map<String, Object> businessMap = new HashMap<>();
        businessMap.put("自我介绍", "自我介绍");
        businessMap.put("居民名称", "居民名称");
        session.setSpeechParam(businessMap);
        ThreadSessionContext.setSession(session);
        PlayEventMessage playEventMessage = new PlayEventMessage();
        nodePrompt.wrap(playEventMessage, null);
        log.info("playList:{}", JSON.toJSON(playEventMessage.getPlaylist()));

        assertEquals(5, playEventMessage.getPlaylist().size());
    }

    @Test
    public void testTextTestwrap() {
        String src = "${喂您好，我是}#{自我介绍}${的助手，请问您是}#{居民名称}${吗？}";
        NodePrompt nodePrompt = new NodePrompt();
        nodePrompt.setText(src);

        String voice = "/outbound/voice/0.mp3";
        String voice1 = "/outbound/voice/1.mp3";
        String voice2 = "/outbound/voice/2.mp3";

        Map<String, String> audioMap = new HashMap<>();
        audioMap.put("喂您好，我是", voice);
        audioMap.put("的助手，请问您是", voice1);
        audioMap.put("吗？", voice2);
        nodePrompt.setAudioList(audioMap);
        InvokeMessageDto invokeMessageDto = new InvokeMessageDto();
        invokeMessageDto.setBusinessType(BUSINESS_TYPE_TEST.getCode());

        FlowSession session = new FlowSession();
        Map<String, Object> businessMap = new HashMap<>();
        businessMap.put("自我介绍", "自我介绍");
        businessMap.put("居民名称", "居民名称");
        session.setSpeechParam(businessMap);
        session.setInvokeMessage(invokeMessageDto);
        ThreadSessionContext.setSession(session);
        PlayEventMessage playEventMessage = new PlayEventMessage();

        nodePrompt.wrap(playEventMessage, null);
        log.info("playText:{}", JSON.toJSON(playEventMessage.getPlayText()));
        log.info("playlist:{}", JSON.toJSON(playEventMessage.getPlaylist()));

        assertEquals(5, playEventMessage.getPlaylist().size());
        assertEquals("喂您好，我是自我介绍的助手，请问您是居民名称吗？", playEventMessage.getPlayText());
    }

    @Test
    public void testInnerWrap() {
        String src = "${喂您好，我是}#{自我介绍},#{昵称},${的助手，请问您是}#{居民名称}${吗？}";
        NodePrompt nodePrompt = new NodePrompt();
        nodePrompt.setText(src);

        String voice = "/outbound/voice/0.mp3";
        String voice1 = "/outbound/voice/1.mp3";
        String voice2 = "/outbound/voice/2.mp3";

        Map<String, String> audioMap = new HashMap<>();
        audioMap.put("喂您好，我是", voice);
        audioMap.put("的助手，请问您是", voice1);
        audioMap.put("吗？", voice2);
        nodePrompt.setAudioList(audioMap);

        FlowSession session = new FlowSession();
        BaseVariable baseVariable1 = new StringVariable();
        baseVariable1.setInternal(true);
        baseVariable1.setDefaultValue("您");
        baseVariable1.setName("昵称");

        BaseVariable baseVariable2 = new StringVariable();
        baseVariable2.setInternal(false);
        baseVariable2.setDefaultValue("自我介绍默认");
        baseVariable2.setName("自我介绍");

        BaseVariable baseVariable3 = new StringVariable();
        baseVariable3.setInternal(false);
        baseVariable3.setDefaultValue("居民名称");
        baseVariable3.setName("居民名称默认");

        Map<String, BaseVariable> map = new HashMap<>();
        map.put("昵称", baseVariable1);
        map.put("自我介绍", baseVariable2);
        map.put("居民名称", baseVariable3);
        session.setVariableMap(map);

        Map<String, Object> businessMap = new HashMap<>();
        businessMap.put("自我介绍", "自我介绍");
        businessMap.put("居民名称", "居民名称");
        session.setSpeechParam(businessMap);
        Map<String, Object> innerMap = new HashMap<>();
        innerMap.put("昵称", "您");
        session.setInnerContext(innerMap);
        ThreadSessionContext.setSession(session);
        PlayEventMessage playEventMessage = new PlayEventMessage();
        nodePrompt.wrap(playEventMessage, null);
        log.info("playList:{}", JSON.toJSON(playEventMessage.getPlaylist()));

        assertEquals(5, playEventMessage.getPlaylist().size());
        assertEquals("自我介绍,您,", playEventMessage.getPlaylist().get(1).getValue());

        businessMap = new HashMap<>();
        session.setSpeechParam(businessMap);
        innerMap = new HashMap<>();
        session.setInnerContext(innerMap);
        ThreadSessionContext.setSession(session);
        playEventMessage = new PlayEventMessage();
        nodePrompt.wrap(playEventMessage, null);
        log.info("getPlayText:{}", JSON.toJSON(playEventMessage.getPlayText()));
        assertEquals("喂您好，我是自我介绍默认,您,的助手，请问您是居民名称吗？", playEventMessage.getPlayText());
        log.info("playList:{}", JSON.toJSON(playEventMessage.getPlaylist()));
        assertEquals("自我介绍默认,您,", playEventMessage.getPlaylist().get(1).getValue());

    }
}