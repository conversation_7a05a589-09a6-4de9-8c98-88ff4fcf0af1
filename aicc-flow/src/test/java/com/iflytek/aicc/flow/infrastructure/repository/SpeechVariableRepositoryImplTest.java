package com.iflytek.aicc.flow.infrastructure.repository;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 方法说明
 *
 * <AUTHOR>
 * @className aicc-platform
 * @date $ $
 */
@RunWith(SpringRunner.class)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class SpeechVariableRepositoryImplTest {

    @Test
    public void selectSpeechVariableList() {
    }
}