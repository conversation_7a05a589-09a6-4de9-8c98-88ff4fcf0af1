package com.iflytek.aicc.flow.service;

import com.iflytek.aicc.flow.application.dto.SpeechFlowDetailDto;
import com.iflytek.aicc.flow.application.service.SpeechFlowManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 类说明
 *
 * <AUTHOR>
 * @date 2023/9/27
 */
@Slf4j
@Service
public class SpeechFlowManagerServiceTest {

    @Autowired
    public SpeechFlowManagerService speechFlowManagerService;

    public SpeechFlowDetailDto speechFlowDetail(Long speechId) {
        return speechFlowManagerService.speechFlowDetail("", speechId, false);
    }

    public String releaseFlow(Long speechId) {
        return speechFlowManagerService.releaseFlow(speechId, "", null);
    }
}
