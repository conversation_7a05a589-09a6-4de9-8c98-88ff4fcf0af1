package com.iflytek.aicc.flow;

import com.iflytek.aicc.flow.api.convertor.FlowExecutorConvertor;
import com.iflytek.aicc.flow.api.param.cti.InvokeMessageParam;
import com.iflytek.aicc.flow.api.vo.cti.FlowResultVO;
import com.iflytek.aicc.flow.application.dto.SpeechFlowDetailDto;
import com.iflytek.aicc.flow.application.service.FlowExecutorService;
import com.iflytek.aicc.flow.common.enums.FlowInvokeEventEnums;
import com.iflytek.aicc.flow.domain.driven.entity.play.EndEventMessage;
import com.iflytek.aicc.flow.domain.driven.entity.play.EventMessage;
import com.iflytek.aicc.flow.domain.driven.entity.play.PlayEventMessage;
import com.iflytek.aicc.flow.service.FlowHeartbeatServiceTest;
import com.iflytek.aicc.flow.service.SpeechFlowManagerServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;

/**
 * 方法说明 单元测试主类
 * -DlogPath=/Users/<USER>/logs
 * -Dspring.application.name=aicc-flow
 * -Dserver.port=18350
 * -Dspring.cloud.nacos.configStringUtils.server-addr=*************:8848
 * -Dspring.cloud.nacos.discovery.server-addr=*************:8848
 * -Dspring.cloud.nacos.discovery.enabled=true
 * -Dspring.cloud.nacos.config.file-extension=yml
 * -Dspring.cloud.nacos.config.namespace=aicc-dev
 * -Dspring.cloud.nacos.config.username=aicc-dev
 * -Dspring.cloud.nacos.config.password=aicc-dev
 * -Dspring.cloud.nacos.discoveryg.file-extension=yml
 * -Dspring.cloud.nacos.discovery.port=18350
 * -Dspring.cloud.nacos.config.shared-dataids=application.yml
 * -Dspring.cloud.nacos.discovery.ip=**********
 * <AUTHOR>
 * @className aicc-platform
 * @date $ $
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@EnableAutoConfiguration
@Slf4j
public class MainServiceTest {

    @Autowired
    public FlowHeartbeatServiceTest flowHeartbeatService;
    @Autowired
    public SpeechFlowManagerServiceTest speechFlowManagerServiceTest;

    @Autowired
    public FlowExecutorService flowExecutorService;


    @Test
    public void heartbeatTest() {
        FlowResultVO<?>  data = flowHeartbeatService.testFlowHearbeatReceive(RandomUtils.nextLong());
        Assertions.assertEquals(0, data.getCode());

        flowHeartbeatService.testDestroySessionByHeartBeat();
    }

    @Test
    public void speechFlowManagerServiceTest() {
        SpeechFlowDetailDto speechFlowDetailDto = speechFlowManagerServiceTest.speechFlowDetail(100000000000113L);
        Assertions.assertNotNull(speechFlowDetailDto);
    }

    /**
     * 基础固定对话流测试
     * 包含场景 多次未识别、结束请求、接听不便逻辑
     * 不包含
     */
    @Test
    public void testNormalFlowApi() {
        //gud
        //模拟固定流程请求
        long callId = System.currentTimeMillis() + RandomUtils.nextLong();
        int seqNo = 0;
        int businessType = 2;
        //先默认对象流
        String speechId = "1528974485725185";
        log.info(callId + "开始测试");
        InvokeMessageParam init = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_INIT.getCode())
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        EventMessage eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(init),
                null);
        Assertions.assertTrue(eventMessage instanceof PlayEventMessage);
        PlayEventMessage playEventMessage = (PlayEventMessage) eventMessage;
        Assertions.assertEquals("100101", playEventMessage.getNodeId());

        // 自我介绍 -随意回答
        seqNo = seqNo + 1;
        InvokeMessageParam stepTwo = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_COLLECT.getCode())
                .collectStatus(FlowInvokeEventEnums.FlowEventCollectStatusEnum.STATUS_NORMAL_TEXT.getCode())
                .content(Collections.singletonList("你的名字是什么"))
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(stepTwo), null);
        Assertions.assertTrue(eventMessage instanceof PlayEventMessage);
        playEventMessage = (PlayEventMessage) eventMessage;
        Assertions.assertEquals("100101", playEventMessage.getNodeId());

        // 自我介绍 -是本人
        seqNo = seqNo + 1;
        InvokeMessageParam stepThree = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_COLLECT.getCode())
                .collectStatus(FlowInvokeEventEnums.FlowEventCollectStatusEnum.STATUS_NORMAL_TEXT.getCode())
                .content(Collections.singletonList("是的"))
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(stepThree), null);
        Assertions.assertTrue(eventMessage instanceof PlayEventMessage);
        playEventMessage = (PlayEventMessage) eventMessage;
        Assertions.assertEquals("100102", playEventMessage.getNodeId());

        //症状询问
        seqNo = seqNo + 1;
        InvokeMessageParam stepfour = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_COLLECT.getCode())
                .collectStatus(FlowInvokeEventEnums.FlowEventCollectStatusEnum.STATUS_NORMAL_TEXT.getCode())
                .content(Collections.singletonList("有症状"))
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(stepfour), null);
        Assertions.assertTrue(eventMessage instanceof PlayEventMessage);
        playEventMessage = (PlayEventMessage) eventMessage;
        Assertions.assertEquals("10010204", playEventMessage.getNodeId());

        //具体症状 1
        seqNo = seqNo + 1;
        InvokeMessageParam stepFive = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_COLLECT.getCode())
                .collectStatus(FlowInvokeEventEnums.FlowEventCollectStatusEnum.STATUS_NORMAL_TEXT.getCode())
                .content(Collections.singletonList(""))
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(stepFive), null);
        Assertions.assertTrue(eventMessage instanceof PlayEventMessage);
        playEventMessage = (PlayEventMessage) eventMessage;
        Assertions.assertEquals("10010204", playEventMessage.getNodeId());

        //具体症状 2
        seqNo = seqNo + 1;
        InvokeMessageParam stepSeven = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_COLLECT.getCode())
                .collectStatus(FlowInvokeEventEnums.FlowEventCollectStatusEnum.STATUS_NORMAL_TEXT.getCode())
                .content(Collections.singletonList("头昏"))
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(stepSeven), null);
        Assertions.assertTrue(eventMessage instanceof PlayEventMessage);
        playEventMessage = (PlayEventMessage) eventMessage;
        Assertions.assertEquals("10010202", playEventMessage.getNodeId());

        //血压询问-1
        seqNo = seqNo + 1;
        InvokeMessageParam stepBloodOne = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_COLLECT.getCode())
                .collectStatus(FlowInvokeEventEnums.FlowEventCollectStatusEnum.STATUS_NORMAL_TEXT.getCode())
                .content(Collections.singletonList(""))
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(stepBloodOne), null);
        Assertions.assertTrue(eventMessage instanceof PlayEventMessage);
        playEventMessage = (PlayEventMessage) eventMessage;
        Assertions.assertEquals("10010202", playEventMessage.getNodeId());

        //血压询问-1
        seqNo = seqNo + 1;
        InvokeMessageParam stepBloodTwo = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_COLLECT.getCode())
                .collectStatus(FlowInvokeEventEnums.FlowEventCollectStatusEnum.STATUS_NORMAL_TEXT.getCode())
                .content(Collections.singletonList(""))
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(stepBloodTwo), null);
        Assertions.assertTrue(eventMessage instanceof PlayEventMessage);
        playEventMessage = (PlayEventMessage) eventMessage;
        Assertions.assertEquals("10010202", playEventMessage.getNodeId());

        //血压询问-1
        seqNo = seqNo + 1;
        InvokeMessageParam stepBlood = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_COLLECT.getCode())
                .collectStatus(FlowInvokeEventEnums.FlowEventCollectStatusEnum.STATUS_NORMAL_TEXT.getCode())
                .content(Collections.singletonList(""))
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(stepBlood), null);
        Assertions.assertTrue(eventMessage instanceof PlayEventMessage);
        playEventMessage = (PlayEventMessage) eventMessage;
        Assertions.assertEquals("10010229", playEventMessage.getNodeId());


        //接通不便
        seqNo = seqNo + 1;
        InvokeMessageParam step = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_COLLECT.getCode())
                .collectStatus(FlowInvokeEventEnums.FlowEventCollectStatusEnum.STATUS_NORMAL_TEXT.getCode())
                .content(Collections.singletonList("我现在不太方便"))
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(step), null);
        Assertions.assertTrue(eventMessage instanceof PlayEventMessage);
        playEventMessage = (PlayEventMessage) eventMessage;
        Assertions.assertEquals("400001", playEventMessage.getNodeId());

        //接通不便 结束
        seqNo = seqNo + 1;
        InvokeMessageParam end = InvokeMessageParam.builder().businessType(businessType)
                .invokeType(FlowInvokeEventEnums.FlowEventEnum.EVENT_END.getCode())
                .collectStatus(FlowInvokeEventEnums.FlowEventCollectStatusEnum.STATUS_NORMAL_TEXT.getCode())
                .content(Collections.singletonList(""))
                .callId(String.valueOf(callId)).seqNo(seqNo).speechId(speechId).build();
        eventMessage = flowExecutorService.executeFlow(FlowExecutorConvertor.convertToInvokeMessageDto(end), null);
        Assertions.assertTrue(eventMessage instanceof EndEventMessage);

        //交互文本验证
    }
}