package com.iflytek.aicc.task.application.job;


import com.alibaba.fastjson.JSONObject;
import com.iflytek.aicc.dto.DictPackageDto;
import com.iflytek.aicc.service.SystemDictApi;
import com.iflytek.aicc.task.application.config.CallDataPushConfig;
import com.iflytek.aicc.task.domain.repository.CallTaskRepository;
import com.iflytek.medicalboot.core.id.UidService;
import com.iflytek.outbound.util.Sm4Util;
import com.iflytek.outbound.vo.TaskVO;
import com.iflytek.outbound.vo.UserInfo;
import com.iflytek.outbound.vo.VarVO;
import com.iflytek.outbound.vo.sms.SmsTaskInfo;
import com.iflytek.outbound.vo.sms.SmsTaskRequest;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述：任务夜间积压告警job
 *
 * <AUTHOR>
 * @date 2024/10/15
 */
@Component
@Slf4j
public class TaskBacklogNightAlarmJobHandler {


    @Value("${task.backlog.total: 100}")
    private Integer total;
    @Value("${plat.name: 开发环境}")
    private String platName;
    @Value("${plat.appId:21729d4e0aa44d34a4d619f8e2a9fed3}")
    private String appId;
    @Value("${plat.secretKey:zjgswjw0001}")
    private String secretKey;
    @Value("${plat.sm4Key:w5xv7[Nmc0Z/3U^Y}")
    public String sm4Key;
    /**
     * 验证码短信模板id
     */
    private static final Long VERIFY_CODE_SMS_ID = 10427L;
    private final CallTaskRepository callTaskRepository;
    private final UidService uidService;
    private final RestTemplate outboundRestTemplate;
    private final SystemDictApi systemDictApi;
    private final CallDataPushConfig callDataPushConfig;


    public TaskBacklogNightAlarmJobHandler(CallTaskRepository callTaskRepository, UidService uidService, RestTemplate outboundRestTemplate,
                                           SystemDictApi systemDictApi, CallDataPushConfig callDataPushConfig) {
        this.callTaskRepository = callTaskRepository;
        this.uidService = uidService;
        this.outboundRestTemplate = outboundRestTemplate;
        this.systemDictApi = systemDictApi;
        this.callDataPushConfig = callDataPushConfig;
    }

    @XxlJob(value = "taskBacklogNightAlarmJobHandler")
    public void execute() {
        XxlJobHelper.log(":xxl-job taskBacklogNightAlarmJobHandler -----------------start-------------------");
        log.info(":xxl-job taskBacklogNightAlarmJobHandler -----------------start-------------------");

        Integer count = callTaskRepository.getUnCalledTaskAtNight();
        if (count < total) {
            XxlJobHelper.log(":xxl-job taskBacklogNightAlarmJobHandler -----------------end, 本次监控未完成任务数：{}", count);
            log.info(":xxl-job taskBacklogNightAlarmJobHandler -----------------end----------------, 本次监控未完成任务数：{}", count);
            XxlJobHelper.handleSuccess("执行成功");
            return;
        }
        log.info(":xxl-job taskBacklogNightAlarmJobHandler，本次监控未完成任务数：{}", count);
        XxlJobHelper.log(":xxl-job taskBacklogNightAlarmJobHandler, 本次监控未完成任务数：{}", count);

        //发送短信告警
        String name = platName + "任务夜间积压告警：";
        String content = "未完成任务数:" + count;
        SmsTaskRequest smsTaskRequest = getSmsTaskRequest();
        //短信变量内容
        List<VarVO> smsVars = getVarVOS(name, content);
        smsTaskRequest.setSmsVars(smsVars);
        //获取字典告警手机号
        DictPackageDto callerAlarmDict = systemDictApi.getByDictValue("caller_alarm");
        if (callerAlarmDict == null || CollectionUtils.isEmpty(callerAlarmDict.getChild())) {
            XxlJobHelper.log(":xxl-job taskBacklogNightAlarmJobHandler,未配置告警号码 -----------------end, 本次监控未完成任务数：{}", count);
            log.info(":xxl-job taskBacklogNightAlarmJobHandler,未配置告警号码 -----------------end----------------, 本次监控未完成任务数：{}", count);
            XxlJobHelper.handleSuccess("执行成功");
            return;
        }
        List<TaskVO> taskVOList = getTaskVOS(callerAlarmDict);
        smsTaskRequest.setTaskDetail(taskVOList);
        String url = md5EncryptUrl(callDataPushConfig.getSmsTaskUrl());
        String answer = null;
        long start = System.currentTimeMillis();
        try {
            Map<String, Object> sendMap = new HashMap<>();
            String sm4 = Sm4Util.encryptStr(JSONObject.toJSONString(smsTaskRequest), "SM4", sm4Key);
            sendMap.put("data", sm4);
            answer = outboundRestTemplate.postForObject(url, sendMap, String.class);
        } finally {
            log.info("reqUrl: {}, reqJson:{},result: {} ,耗时：{}", url, JSONObject.toJSON(smsTaskRequest), answer, System.currentTimeMillis() - start);
            XxlJobHelper.log(":xxl-job reqUrl: {}, reqJson:{},result: {} ,耗时：{}",
                    url, JSONObject.toJSON(smsTaskRequest), answer, System.currentTimeMillis() - start);
        }
        XxlJobHelper.log(":xxl-job taskBacklogNightAlarmJobHandler -----------------end, 本次监控未完成任务数：{}", count);
        log.info(":xxl-job taskBacklogNightAlarmJobHandler -----------------end----------------, 本次监控未完成任务数：{}", count);
        XxlJobHelper.handleSuccess("执行成功");
    }

    private List<TaskVO> getTaskVOS(DictPackageDto callerAlarmDict) {
        List<TaskVO> taskVOList = new ArrayList<>();
        for (DictPackageDto callee : callerAlarmDict.getChild()) {
            TaskVO taskVO = new TaskVO();
            taskVO.setDwellerName("JOB短信告警");
            taskVO.setPhone(callee.getDictValue());
            taskVO.setBusinessTaskId(uidService.getUID());
            taskVOList.add(taskVO);
        }
        return taskVOList;
    }

    private List<VarVO> getVarVOS(String name, String content) {
        List<VarVO> smsVars = new ArrayList<>();
        VarVO nameVar = new VarVO();
        nameVar.setName("name");
        nameVar.setContent(name);
        smsVars.add(nameVar);
        VarVO contentVar = new VarVO();
        contentVar.setName("content");
        contentVar.setContent(content);
        smsVars.add(contentVar);
        return smsVars;
    }

    private SmsTaskRequest getSmsTaskRequest() {
        SmsTaskRequest smsTaskRequest = new SmsTaskRequest();
        smsTaskRequest.setSmsId(VERIFY_CODE_SMS_ID);
        smsTaskRequest.setSmsName("系统模板");
        smsTaskRequest.setOutPlatform(0);
        smsTaskRequest.setPlatformSmsId(VERIFY_CODE_SMS_ID.toString());
        //用户信息
        UserInfo userInfo = getUserInfo();
        smsTaskRequest.setUserInfo(userInfo);
        //任务基础信息
        SmsTaskInfo smsTaskInfo = getSmsTaskInfo();
        smsTaskRequest.setTaskInfo(smsTaskInfo);
        return smsTaskRequest;
    }

    private UserInfo getUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setLoginName("系统账号");
        userInfo.setUserId("9f9304266075409c881127fe3e05d159");
        userInfo.setUserName("告警验证码等专用");
        userInfo.setOrgId("51cba5a7011343ba99ad63b30a19e1fa");
        userInfo.setOrgName("智慧·社区");
        return userInfo;
    }

    private SmsTaskInfo getSmsTaskInfo() {
        SmsTaskInfo smsTaskInfo = new SmsTaskInfo();
        smsTaskInfo.setFollowType(0);
        smsTaskInfo.setTaskType(4);
        smsTaskInfo.setTaskSendFlag(0);
        String taskTime = new DateTime().toString("yyyy-MM-dd HH:mm:ss");
        smsTaskInfo.setTaskTime(taskTime);
        return smsTaskInfo;
    }

    private String md5EncryptUrl(String url) {
        long timestamp = System.currentTimeMillis();
        String nonce = String.valueOf(uidService.getUID()) + (int) (Math.random() * 100);
        String sign = DigestUtils.md5DigestAsHex((appId + timestamp + nonce + secretKey).getBytes());
        String prefix = "?";
        if (url.contains("?")) {
            prefix = "&";
        }
        return url + prefix + "appId=" + appId + "&timestamp=" + timestamp + "&nonce=" + nonce + "&sign=" + sign + "&X-encrypt=1";
    }
}
