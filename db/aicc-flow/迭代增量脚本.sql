-- 迭代增量脚本从v.1.5.6开始 不区分表结构、初始化数据

-- v1.5.7迭代
ALTER TABLE tb_aicc_speech_variable RENAME COLUMN value_type TO internal;

ALTER TABLE tb_aicc_speech_variable
    ADD COLUMN default_value varchar(255);

COMMENT ON COLUMN tb_aicc_speech_variable.internal IS '变量类型 0:非内部变量 1:内部变量';

COMMENT ON COLUMN tb_aicc_speech_variable.default_value IS '变量默认值';

ALTER TABLE tb_aicc_speech_nlp_interface
ALTER COLUMN name TYPE varchar(100) COLLATE pg_catalog.default;

--标签库
ALTER TABLE tb_aicc_speech_label ALTER COLUMN speech_id DROP NOT NULL;
ALTER TABLE tb_aicc_speech_label ALTER COLUMN tag_type DROP NOT NULL;

INSERT INTO tb_aicc_speech_label (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, speech_id, code, name, values, tag_type, deleted) VALUES (NULL, NULL, '100001', '2023-11-15 10:31:33.009', '1195447063199744', '2023-11-16 19:05:25.104', 1554237038182400, NULL, 'COMPLETED_ANSWER', '完整回答', 'COMPLETED_ANSWER', NULL, 0);
INSERT INTO tb_aicc_speech_label (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, speech_id, code, name, values, tag_type, deleted) VALUES (NULL, NULL, '100001', '2023-11-16 09:39:42.073', '1195447063199744', '2023-11-16 19:05:49.213', 1554935717150720, NULL, 'NORMAL_ANSWER', '正常回答', 'NORMAL_ANSWER', NULL, 0);

-- V1.6.4版本
ALTER TABLE "tb_aicc_speech_voice"
ALTER COLUMN "voice_content" TYPE varchar(1000) COLLATE "pg_catalog"."default",
  ALTER COLUMN "voice_url" TYPE varchar(1000) COLLATE "pg_catalog"."default";

-- 话术片段增加流程文件地址
ALTER TABLE aicc_flow.tb_aicc_speech_fragment
ADD COLUMN flow_file_path VARCHAR(500) NULL ,
ADD COLUMN speech_label VARCHAR(255) NULL  ;

COMMENT ON column aicc_flow.tb_aicc_speech_fragment.flow_file_path is '流程文件文件地址;流程文件minio地址';
COMMENT ON column aicc_flow.tb_aicc_speech_fragment.speech_label is '话术标签' ;



-- 大模型模版表
CREATE TABLE IF not EXISTS tb_aicc_llm_prompt_template(
    id int8 NOT NULL,
    name VARCHAR(255),
    template_content TEXT NOT NULL,
    variables VARCHAR(1024),
    description VARCHAR(255),
    default_flag int2 default 0,
    tenant_id VARCHAR(64),
    revision int2,
    created_by VARCHAR(64) NOT NULL,
    created_time TIMESTAMP NOT NULL,
    updated_by VARCHAR(64),
    updated_time TIMESTAMP,
    PRIMARY KEY (id)
    );

COMMENT ON TABLE tb_aicc_llm_prompt_template IS '大模型prompt模版配置表';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.id IS '模版ID';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.name IS '模版名称';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.template_content IS '模版内容';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.variables IS '变量列表';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.description IS '模版描述';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.default_flag IS '是否默认模版 0:否 1:是';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.tenant_id IS '租户号';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.revision IS '乐观锁';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.created_by IS '创建人';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.created_time IS '创建时间';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.updated_by IS '更新人';
COMMENT ON COLUMN tb_aicc_llm_prompt_template.updated_time IS '更新时间';

-- 初始化prompt模版，默认版本
delete from tb_aicc_llm_prompt_template where id = ***********;
INSERT INTO "tb_aicc_llm_prompt_template" ("id", "name", "template_content", "variables", "description", "default_flag", "tenant_id", "revision", "created_by", "created_time", "updated_by", "updated_time") VALUES (***********, '默认模版', '这是一个智能外呼场景，下面的"历史对话内容"是医生和患者的历史对话，"建议回复"是通过规则设定的针对用户问题的一个建议回复。
##历史对话内容
${historyText}##建议回复
${examplePlayText}
##任务要求
1. 以纯文本进行回复；
2. 回复整体要保持礼貌；
3. 回答字数和建议回复相差不超过20个字；
4. 直接进行回答，不需要加 “医生：”的前缀；
5. 不要询问无关内容；
6. 回复不要过于冗余，如果建议回复以问句结尾，你的回复也需要以问句结尾，不需要在问句后面再补充如“希望您一切都好”等，也不要把一个问题拆开成多个子问题，问题数量和建议回复的问题数量保持一致即可；如果建议回复以陈述句结尾，你的回复也需要以陈述句结尾；
7. 不要擅自推测性别，不要主观性的加“先生”、“女士”等后缀，尽量避免称呼患者的名字，用您/他/她等称呼即可；
8. 你可以从“历史对话内容”和“建议回复”中推理出讨论的患者是当前正在通话中的人，还是在讨论其他人，从而区分您和他/她，比如：如果在讨论的是他的饮食/自理情况/症状等，就需要用他的饮食/自理情况/症状等，而不是您的饮食/自理情况/症状等；
9. 不要变动建议回复的一些关键词，比如：不要把“上升”、“下降”换成“变化”，不要把“能否自理”换成“能否照顾好自己”等，直接使用建议回复中的词即可；
10. 如果需要，可以在你给出的回复前面，对患者的情况和问题进行一定的回应，让整体的回复更个性化。比如：患者患病或者身心不舒服的时候，给出一定的安慰共情；患者身心健康或者症状痊愈的时候，给出一定的积极回应。但是要确保这些回应加上之后，不会改变回复的主体意思，整体语义和内容尽量不变。', '', '默认prompt', 1, NULL, NULL, '111111', '2024-01-28 17:34:56', NULL, NULL);

-- v1.6.7
-- 发送任务失败表
DROP TABLE IF EXISTS "tb_fail_compensate_message";
CREATE TABLE "tb_fail_compensate_message" (
      "id" int8 NOT NULL,
      "param" text ,
      "call_back_url" varchar(120) ,
      "priority_level" int2 DEFAULT 1,
      "retry_times" int2 NOT NULL DEFAULT 0,
      "exec_status" int2 NOT NULL DEFAULT 0,
      "response_result" varchar(200) ,
      "remark" varchar(200) ,
      "created_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "created_by" varchar(32)  NOT NULL,
      "updated_time" timestamp(6),
      "updated_by" varchar(32) ,
      "deleted" int2 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "tb_fail_compensate_message"."param" IS '''参数:参数格式{\r\n“method”:”方法名”\r\n“param”:{}\r\n}\r\n其中method为调用的方法名称，利于使用反射,减少if else使用\r\nparam：body参数'',';
COMMENT ON COLUMN "tb_fail_compensate_message"."call_back_url" IS '回调接口';
COMMENT ON COLUMN "tb_fail_compensate_message"."priority_level" IS '优先等级1.底 2 中 3 高';
COMMENT ON COLUMN "tb_fail_compensate_message"."retry_times" IS '重试次数';
COMMENT ON COLUMN "tb_fail_compensate_message"."exec_status" IS '执行状态：0 未开始 1 成功 2失败';
COMMENT ON COLUMN "tb_fail_compensate_message"."response_result" IS '返回结果';
COMMENT ON COLUMN "tb_fail_compensate_message"."remark" IS '备注信息';
COMMENT ON COLUMN "tb_fail_compensate_message"."created_time" IS '创建时间';
COMMENT ON COLUMN "tb_fail_compensate_message"."created_by" IS '创建者';
COMMENT ON COLUMN "tb_fail_compensate_message"."updated_time" IS '更新时间';
COMMENT ON COLUMN "tb_fail_compensate_message"."updated_by" IS '更新者';
COMMENT ON COLUMN "tb_fail_compensate_message"."deleted" IS '是否删除 0 否 1是';

-- ----------------------------
-- Primary Key structure for table tb_fail_compensate_message
-- ----------------------------
ALTER TABLE "tb_fail_compensate_message" ADD CONSTRAINT "tb_fail_compensate_message_pkey" PRIMARY KEY ("id");

-- 分区表脚本
CREATE INDEX tb_aicc_speech_node_result_created_time_idx ON tb_aicc_speech_node_result (created_time);
ALTER TABLE tb_aicc_speech_node_result RENAME TO tb_aicc_speech_node_result_bak;
-- 删除旧表主键和唯一索引
ALTER TABLE "aicc_flow"."tb_aicc_speech_node_result_bak" DROP CONSTRAINT "tb_aicc_speech_node_result_pkey";

CREATE TABLE tb_aicc_speech_node_result ( LIKE tb_aicc_speech_node_result_bak INCLUDING ALL ) PARTITION BY RANGE (created_time);
-- 创建电话默认子分区
CREATE TABLE tb_aicc_speech_node_result_default PARTITION OF tb_aicc_speech_node_result DEFAULT;
ALTER TABLE tb_aicc_speech_node_result_default ADD PRIMARY KEY ("id");

-- 注意数据的最早时间和需要创建的分区表时间
DO $$
DECLARE
    start_date DATE := CURRENT_DATE;
    i INT;
BEGIN
    WHILE start_date <= CURRENT_DATE + INTERVAL '2 month' LOOP
        i := i + 1;
        EXECUTE format('CREATE TABLE %s PARTITION OF tb_aicc_speech_node_result FOR VALUES FROM (''%s'') TO (''%s'')', format('tb_aicc_speech_node_result_y%sm%s', EXTRACT(YEAR FROM start_date), to_char(start_date, 'MM')), to_char(start_date, 'yyyy-MM-dd'), to_char( DATE_TRUNC('month', start_date)+INTERVAL '1' MONTH, 'yyyy-mm-dd'));
        EXECUTE format('ALTER TABLE %s ADD PRIMARY KEY ("id")', format('tb_aicc_speech_node_result_y%sm%s', EXTRACT(YEAR FROM start_date), to_char(start_date, 'MM')));
        start_date = to_char( DATE_TRUNC('month', start_date)+INTERVAL '1' MONTH, 'yyyy-mm-dd')::date;
    END LOOP;
END $$;
-- 数据迁移
insert into tb_aicc_speech_node_result (select * from tb_aicc_speech_node_result_bak );
-- 删除旧表
DROP TABLE tb_aicc_speech_node_result_bak;

-- 话术侧变量配置内增加备注列
ALTER TABLE tb_aicc_speech_variable ADD remark varchar(200) NULL;
COMMENT ON COLUMN tb_aicc_speech_variable.remark IS '备注';

--话术内知识库
ALTER TABLE tb_aicc_speech_knowledge ADD knowledge_type int2 DEFAULT 0 NOT NULL;
COMMENT ON COLUMN tb_aicc_speech_knowledge.knowledge_type IS '0-全局知识库 1-话术知识库';
ALTER TABLE tb_aicc_speech_knowledge ADD speech_id int8 NULL;
COMMENT ON COLUMN tb_aicc_speech_knowledge.speech_id IS '话术id';

-- V1.6.8
-- 字典表
CREATE TABLE tb_aicc_system_dict (
	id int4 NOT NULL, -- 字典表id，唯一标识
	dict_name varchar(200) NOT NULL, -- 字典类型或者其他类型（人群、话术、短信）
	dict_value varchar(8000) NOT NULL, -- 类型的值
	pid int4 NOT NULL DEFAULT '-1'::integer, -- 类型的标识，属于人群类型、话术类型、短信类型等
	dict_flag int2 NOT NULL DEFAULT 0, -- 是否是字典类型
	rank_id int4 NOT NULL DEFAULT 0,
	remark varchar(200) NULL DEFAULT NULL::character varying, -- 备注
	deleted int2 NULL DEFAULT 0,
	general_field1 varchar(255) NULL DEFAULT NULL::character varying, -- 通用字段1
	general_field2 varchar(255) NULL DEFAULT NULL::character varying, -- 通用字段2
	general_field3 varchar(255) NULL DEFAULT NULL::character varying,
	create_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP,
	creator varchar(32) NULL DEFAULT NULL::character varying,
	update_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP,
	updater varchar(32) NULL DEFAULT NULL::character varying,
	CONSTRAINT tb_aicc_system_dict_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_pid_value ON tb_aicc_system_dict USING btree (pid, dict_value);
-- Column comments
COMMENT ON COLUMN tb_aicc_system_dict.id IS '字典表id，唯一标识';
COMMENT ON COLUMN tb_aicc_system_dict.dict_name IS '字典类型或者其他类型（人群、话术、短信）';
COMMENT ON COLUMN tb_aicc_system_dict.dict_value IS '类型的值';
COMMENT ON COLUMN tb_aicc_system_dict.pid IS '类型的标识，属于人群类型、话术类型、短信类型等';
COMMENT ON COLUMN tb_aicc_system_dict.dict_flag IS '是否是字典类型';
COMMENT ON COLUMN tb_aicc_system_dict.remark IS '备注';
COMMENT ON COLUMN tb_aicc_system_dict.general_field1 IS '通用字段1';
COMMENT ON COLUMN tb_aicc_system_dict.general_field2 IS '通用字段2';

-- 大模型模版配置
-- 初始化prompt模版，默认版本
delete from tb_aicc_llm_prompt_template where id in (***********,***********);
INSERT INTO "tb_aicc_llm_prompt_template" ("id", "name", "template_content", "variables", "description", "default_flag", "tenant_id", "revision", "created_by", "created_time", "updated_by", "updated_time") VALUES (***********, '默认模版', '这是一个智能外呼场景，下面的"历史对话内容"是医生和患者的历史对话，"建议回复"是通过规则设定的针对用户问题的一个建议回复。
##历史对话内容
${historyText}##建议回复
${examplePlayText}
##任务要求
1. 以纯文本进行回复；
2. 回复整体要保持礼貌；
3. 回答字数和建议回复相差不超过20个字；
4. 直接进行回答，不需要加 “医生：”的前缀；
5. 不要询问无关内容；
6. 回复不要过于冗余，如果建议回复以问句结尾，你的回复也需要以问句结尾，不需要在问句后面再补充如“希望您一切都好”等，也不要把一个问题拆开成多个子问题，问题数量和建议回复的问题数量保持一致即可；如果建议回复以陈述句结尾，你的回复也需要以陈述句结尾；
7. 不要擅自推测性别，不要主观性的加“先生”、“女士”等后缀，尽量避免称呼患者的名字，用您/他/她等称呼即可；
8. 你可以从“历史对话内容”和“建议回复”中推理出讨论的患者是当前正在通话中的人，还是在讨论其他人，从而区分您和他/她，比如：如果在讨论的是他的饮食/自理情况/症状等，就需要用他的饮食/自理情况/症状等，而不是您的饮食/自理情况/症状等；
9. 不要变动建议回复的一些关键词，比如：不要把“上升”、“下降”换成“变化”，不要把“能否自理”换成“能否照顾好自己”等，直接使用建议回复中的词即可；
10. 如果需要，可以在你给出的回复前面，对患者的情况和问题进行一定的回应，让整体的回复更个性化。比如：患者患病或者身心不舒服的时候，给出一定的安慰共情；患者身心健康或者症状痊愈的时候，给出一定的积极回应。但是要确保这些回应加上之后，不会改变回复的主体意思，整体语义和内容尽量不变。', '', '默认prompt', 1, NULL, NULL, '111111', '2024-01-28 17:34:56', NULL, NULL);

INSERT INTO "tb_aicc_llm_prompt_template" ("id", "name", "template_content", "variables", "description", "default_flag", "tenant_id", "revision", "created_by", "created_time", "updated_by", "updated_time") VALUES (***********, '默认模版', '下面是一个医院打电话给用户进行随访的场景，下面的"医生问题"是医生提出的问题，"用户回答"是用户给出的回答。
##医生问题
医生：${currentQuestion}
##用户回答
用户：${currentAnswer}
对于用户的回答，预先定义了如下的“选择逻辑和话术节点”供医生选择，作为医生下一个要说的话术节点。
##选择逻辑和话术节点
${nextQuestion}若用户的说法不在上述话术的范围中，则话术节点为：拒识
请你根据“选择逻辑和话术节点”中提供的选择逻辑，选择一个最合适的话术节点。只输出话术节点的名称即可。', '', '默认prompt', 0, NULL, NULL, '111111', '2024-01-28 17:34:56', NULL, NULL);
-- 话术片段新增字段
ALTER TABLE tb_aicc_speech_fragment ADD fragment_label varchar(255) NULL;
COMMENT ON COLUMN tb_aicc_speech_fragment.fragment_label IS '片段标签';
ALTER TABLE tb_aicc_speech_fragment ADD status int2 NULL;
COMMENT ON COLUMN tb_aicc_speech_fragment.status IS '状态 1-待发布 2-已发布';
ALTER TABLE tb_aicc_speech_fragment ADD "type" int2 NULL;
COMMENT ON COLUMN tb_aicc_speech_fragment."type" IS '类型 1-单个 2-组合';

-- 新增字典
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(671856089, '病种', 'disease_type', -1, 1, 0, '', 0, NULL, NULL, NULL, '2024-06-11 15:24:55.983', '1650065517969408', '2024-06-11 15:24:55.983', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(276463857, '场景', 'scene_type', -1, 1, 1, '', 0, NULL, NULL, NULL, '2024-06-11 15:25:06.182', '1650065517969408', '2024-06-11 15:25:06.182', '1650065517969408');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(823517178, 'SBC地址', 'sbc_address', -1, 1, 0, '', 0, NULL, NULL, NULL, '2024-06-17 14:31:52.542', '100001', '2024-06-17 14:31:52.542', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(650181956, 'Qetesh_10.100.123.101', '1', 823517178, 1, 0, '', 0, NULL, NULL, NULL, '2024-06-17 14:32:13.950', '100001', '2024-06-17 14:32:13.950', NULL);

-- v1.6.9
-- 发布记录表增加类型字段
ALTER TABLE aicc_flow.tb_aicc_speech_publish_record ADD kind int2 NULL;
COMMENT ON COLUMN aicc_flow.tb_aicc_speech_publish_record.kind IS '类型 1-话术 2-片段';
-- 当前记录kind全部刷成1 话术
update tb_aicc_speech_publish_record set kind = 1;

-- v1.7.0 动态话术
ALTER TABLE "aicc_flow"."tb_aicc_speech_base"
  ADD COLUMN "speech_type" int2 DEFAULT 0,
  ADD COLUMN "publish_time" timestamp DEFAULT NULL,
  ADD COLUMN "publish_by" varchar(64) DEFAULT '',
  ADD COLUMN "speech_fragment_id" varchar(512) DEFAULT '';
COMMENT ON COLUMN "aicc_flow"."tb_aicc_speech_base"."speech_type" IS '话术类型 0-话术流程 1-动态话术';
COMMENT ON COLUMN "aicc_flow"."tb_aicc_speech_base"."publish_time" IS '发布时间';
COMMENT ON COLUMN "aicc_flow"."tb_aicc_speech_base"."publish_by" IS '发布人';
COMMENT ON COLUMN "aicc_flow"."tb_aicc_speech_base"."speech_fragment_id" IS '话术问题id，逗号分开例如：1，2，3';

-- v1.7.3 话术片段更新提示
ALTER TABLE tb_aicc_speech_fragment
  ADD COLUMN nlp_node_id varchar(200) DEFAULT '',
  ADD COLUMN publish_time timestamp(6) DEFAULT NULL,
  ADD COLUMN publish_by varchar(64) DEFAULT '';

COMMENT ON COLUMN tb_aicc_speech_fragment.nlp_node_id IS '语义节点id';
COMMENT ON COLUMN tb_aicc_speech_fragment.publish_time IS '发布时间';
COMMENT ON COLUMN tb_aicc_speech_fragment.publish_by IS '发布人';

update tb_aicc_speech_fragment set publish_by = updated_by,publish_time = updated_time where status = 2;


DROP TABLE IF EXISTS tb_aicc_speech_base_fragment_rel;
CREATE TABLE tb_aicc_speech_base_fragment_rel(
    id INT8 NOT NULL,
    speech_id INT8 NOT NULL,
    speech_fragment_id INT8 NOT NULL,
    rel_state INT2 NOT NULL DEFAULT  0,
    created_by varchar(64) NOT NULL,
    created_time timestamp NOT NULL DEFAULT  CURRENT_TIMESTAMP,
    updated_by varchar(64),
    updated_time timestamp,
    deleted int2 DEFAULT  0,
    PRIMARY KEY (id)
);

COMMENT ON TABLE tb_aicc_speech_base_fragment_rel IS '话术流程与话术问题关系表';
COMMENT ON COLUMN tb_aicc_speech_base_fragment_rel.id IS '主键';
COMMENT ON COLUMN tb_aicc_speech_base_fragment_rel.speech_id IS '话术ID';
COMMENT ON COLUMN tb_aicc_speech_base_fragment_rel.speech_fragment_id IS '话术片段id';
COMMENT ON COLUMN tb_aicc_speech_base_fragment_rel.rel_state IS '话术与片段状态 0-正常 1-待处理（片段修改了，动态话术待处理）';
COMMENT ON COLUMN tb_aicc_speech_base_fragment_rel.created_by IS '创建人';
COMMENT ON COLUMN tb_aicc_speech_base_fragment_rel.created_time IS '创建时间';
COMMENT ON COLUMN tb_aicc_speech_base_fragment_rel.updated_by IS '更新人';
COMMENT ON COLUMN tb_aicc_speech_base_fragment_rel.updated_time IS '更新时间';
COMMENT ON COLUMN tb_aicc_speech_base_fragment_rel.deleted IS '是否删除;0:正常  1:已删除';

CREATE INDEX idx_speech_id ON tb_aicc_speech_base_fragment_rel(speech_id);
CREATE INDEX idx_fragment_id ON tb_aicc_speech_base_fragment_rel(speech_fragment_id);


-- v1.7.4
-- 话术片段增加发布流程文件地址
ALTER TABLE tb_aicc_speech_fragment ADD publish_file_path varchar(500) NULL;
COMMENT ON COLUMN tb_aicc_speech_fragment.publish_file_path IS '流程文件发布文件地址;流程文件minio地址';
ALTER TABLE tb_aicc_speech_fragment ADD md5_value varchar(64) NULL;
COMMENT ON COLUMN tb_aicc_speech_fragment.md5_value IS 'md5;用来判断该话术是否被修改';

--更新MD5和value
update tb_aicc_speech_publish_record set md5_value = substr(md5(random()::text), 1, 32)  where kind = 2 and md5_value is null;
-- 刷新片段表文件地址和MD5值
UPDATE tb_aicc_speech_fragment as ta
SET publish_file_path = tb.flow_file_path,
    md5_value = tb.md5_value
FROM (
    SELECT speech_id AS id, MAX(flow_file_path) AS flow_file_path, MAX(md5_value) AS md5_value
    FROM tb_aicc_speech_publish_record
    WHERE kind = 2
    GROUP BY speech_id
) tb
WHERE ta.id = tb.id;
-- 声音复刻增加 发音人类型
ALTER TABLE tb_aicc_voice_config ADD voice_type int2 DEFAULT 0 NULL;
COMMENT ON COLUMN tb_aicc_voice_config.voice_type IS '发音人类型 0- 系统发音人 1-复刻发音人';


--患者画像配置表
DROP TABLE IF EXISTS tb_aicc_patient_portrait_config;
CREATE TABLE tb_aicc_patient_portrait_config(
    id int8 NOT NULL,
    parent_id int8 NOT NULL DEFAULT  0,
    name VARCHAR(255),
    code VARCHAR(255),
    created_time TIMESTAMP DEFAULT  CURRENT_TIMESTAMP,
    operator VARCHAR(32),
    updated_time TIMESTAMP DEFAULT  CURRENT_TIMESTAMP,
    updater VARCHAR(32),
    deleted_time TIMESTAMP,
    deleted int8 NOT NULL DEFAULT  0,
    PRIMARY KEY (id)
);

CREATE INDEX idx_parent_id ON tb_aicc_patient_portrait_config(parent_id);

COMMENT ON TABLE tb_aicc_patient_portrait_config IS '患者画像配置表';
COMMENT ON COLUMN tb_aicc_patient_portrait_config.id IS '主键id;主键';
COMMENT ON COLUMN tb_aicc_patient_portrait_config.parent_id IS '父id';
COMMENT ON COLUMN tb_aicc_patient_portrait_config.name IS '画像名称';
COMMENT ON COLUMN tb_aicc_patient_portrait_config.code IS '画像编码';
COMMENT ON COLUMN tb_aicc_patient_portrait_config.created_time IS '创建时间';
COMMENT ON COLUMN tb_aicc_patient_portrait_config.operator IS '操作者';
COMMENT ON COLUMN tb_aicc_patient_portrait_config.updated_time IS '更新时间';
COMMENT ON COLUMN tb_aicc_patient_portrait_config.updater IS '更新者';
COMMENT ON COLUMN tb_aicc_patient_portrait_config.deleted_time IS '删除时间';
COMMENT ON COLUMN tb_aicc_patient_portrait_config.deleted IS '是否删除';

-- 患者画像内容
DROP TABLE IF EXISTS tb_aicc_patient_portrait_content;
CREATE TABLE tb_aicc_patient_portrait_content(
    id int8 NOT NULL,
    config_id int8 NOT NULL,
    content VARCHAR(255),
    created_time TIMESTAMP DEFAULT  CURRENT_TIMESTAMP,
    operator VARCHAR(32),
    updated_time TIMESTAMP DEFAULT  CURRENT_TIMESTAMP,
    updater VARCHAR(32),
    deleted_time TIMESTAMP,
    deleted int8 NOT NULL DEFAULT  0,
    PRIMARY KEY (id)
);
CREATE INDEX idx_config_id ON tb_aicc_patient_portrait_content(config_id);
COMMENT ON TABLE tb_aicc_patient_portrait_content IS '患者画像内容';
COMMENT ON COLUMN tb_aicc_patient_portrait_content.id IS '主键id;主键';
COMMENT ON COLUMN tb_aicc_patient_portrait_content.config_id IS '所属画像类别id';
COMMENT ON COLUMN tb_aicc_patient_portrait_content.content IS '画像内容';
COMMENT ON COLUMN tb_aicc_patient_portrait_content.created_time IS '创建时间';
COMMENT ON COLUMN tb_aicc_patient_portrait_content.operator IS '操作者';
COMMENT ON COLUMN tb_aicc_patient_portrait_content.updated_time IS '更新时间';
COMMENT ON COLUMN tb_aicc_patient_portrait_content.updater IS '更新者';
COMMENT ON COLUMN tb_aicc_patient_portrait_content.deleted_time IS '删除时间';
COMMENT ON COLUMN tb_aicc_patient_portrait_content.deleted IS '是否删除';


INSERT INTO tb_aicc_llm_prompt_template
(id, name, template_content, variables, description, default_flag, tenant_id, revision, created_by, created_time, updated_by, updated_time)
VALUES(***********, '默认模版', '##情境背景：这是一个客服用户对话场景，目的是对用户在医院看病后不满意的具体原因进行分类，请阅读下面的客服用户对话：
客服：${currentQuestion}
用户的回答：${currentAnswer}
##根据上述对话，按照你的理解，分析对话中"用户的回答"不满意的原因，首先进行二级分类，要求如下：
1.1、不满意原因（二级分类）归类如下：
{健康教育,费用管理,餐饮服务,服务模式,护士沟通,问卷采集,医生沟通,环境卫生,陪护相关问题,隐私保护,环境设施,疼痛管理,医保服务,整体评价,人员数量,等待时间,医德医风,检查检验,护理质量,安全管理,停车问题,工作责任心,治疗结果,信息告知,就诊秩序,服务态度,120相关问题,需求回应,挂号问题,药品设备}
1.2、如果用户的回答包含多个不满（多个意图），均需要列出；
1.3、如果用户的回答为”满意“或”没有什么不满意的“，则结果为满意；
1.4、如果用户的回答为”什么都不满意“或者”不太满意“，则结果为不满意；
1.5、如果都不属于以上的范畴，则归类为无关回答，不用进行解释；
1.6、如果用户的回答仅包含对“效率”的负面表述，或者存在“找不到医生”等需求没有得到回应，则二级分类结果为"需求回应"；
1.7、二级分类仅为1.1中的要求、”满意“、“不满意”和“无关回答”；
1.8、若二级分类为无关回答，则仅需输出“无关回答”；


然后根据二级分类结果和你理解的客服用户对话，匹配对应的三级分类，要求和格式如下：
2.1、具体分类（三级分类）归类如下：
{整体评价:整体评价
环境设施:建筑功能;建筑布局;环境设施的其他问题;建筑空间;硬件设施;指示标识
护理质量:护理操作规范性;护理专业水平;护理质量其他问题;治疗管理;护理过程细致程度
问卷采集:问卷采集问题
工作责任心:医护工作责任心;未特指人员工作责任心;护士责任心;医生责任心
挂号问题:技术支持和故障响应;挂号可及性;挂号系统;挂号费用;特殊群体挂号;挂号信息提供;挂号隐私保护;挂号途径;挂号其他问题;挂号就诊指引;退号和改签;导诊;挂号流程
停车问题:停车场管理;停车场位置;停车收费;停车指引;停车的其他问题;停车场秩序;车位数量
等待时间:就餐等待时间;床位安排等待时间;接诊等待时间;未特指环节的等待时间;取药等待时间;检查检验结果等待时间;办理手续等待时间;检查检验等待时间;分诊等待时间;停车场等待时间;挂号等待时间;治疗或手术等待时间;电梯等待时间
医生沟通:信息告知;沟通频率
隐私保护:诊疗过程;个人信息;隐私保护其他问题;检验结果;病历资料
餐饮服务:价格合理性;菜单种类与选择;其他餐饮问题;餐饮环境;配餐与送餐服务;食物营养搭配;食物质量与卫生;口味问题
费用管理:收费规则及明细透明度;费用其他问题;费用咨询与解释;退费流程合理性;支付方式的多样性;费用纠纷处理;收费合理性
医德医风:医德医风
环境卫生:绿化与生态环境;供水和水质管理;异味管理;病房清洁度;公共区域清洁度;卫生间清洁度;病房区域噪音管理;废物处理;公共区域噪音管理;通风状况管理;病媒生物控制;未特指区域的环境卫生;照明和色彩;私密性和个人空间
服务模式:延续性服务;院内服务模式
检查检验:检查信息沟通;检验结果的准确性;检验检查其他问题;检查检验预约便利性;检查过程舒适度;检查项目合理性
健康教育:疾病知识宣教;健康政策宣教;药物使用宣教;康复宣教;生活方式宣教;预防宣教;治疗方案宣教
治疗结果:治疗持续性;治疗方案合理性;治疗结果
药品设备:医疗设备数量;药品设备的其他问题;药品种类;医疗设备先进性
人员数量:医务人员数量;其他工作人员数量
安全管理:患者转运安全;医疗设备设施安全管理;医疗质量安全;无障碍/辅助设施标识;未特指环节的安全管理;医院感染控制;日常安全/意外伤害安全管理;物理安全措施管理
医保服务:医保结算方式及流程;医保政策宣教与告知;医保服务的其他问题;报销范围及比例
就诊秩序:食堂秩序;检查秩序;诊疗秩序;转诊跨院秩序;入出院秩序;住院病房秩序;服务衔接秩序;取药秩序;挂号秩序;候诊秩序;未特指环节或区域的就诊秩序
需求回应:回应速度;处理措施
陪护相关问题:陪护相关问题
服务态度:窗口人员服务态度;医生服务态度;安保人员服务态度;投诉与意见反馈工作人员服务态度;导医人员服务态度;后勤人员服务态度;未特指人员服务态度;保洁人员服务态度;医护人员服务态度;护士服务态度
护士沟通:沟通频率
信息告知:意见反馈与投诉渠道;医疗服务流程;信息告知其他问题;医务人员;医疗风险与知情同意;医院规章制度
120相关问题:120相关问题
疼痛管理:疼痛管理效果;疼痛管理及时性;疼痛管理意识
}
2.2、如果用户的回答包含多个不满（多个意图），均需要列出；
2.3、如果用户的回答为”满意“或”没有什么不满意的“等，则结果为满意；
2.4、如果用户的回答为”什么都不满意“或者”不太满意“等，且没有说明具体原因，则结果为不满意；
2.5、输出严格遵循先匹配要求1.1，然后按二级分类匹配要求2.1的三级分类，必须一一对应，即三级分类必须严格属于二级分类，格式为“不满意原因-具体分类”；
2.6、如果用户的回答仅匹配到不满意原因，则输出仅为不满意原因，无需进行分类；
2.7、如果都不属于以上的范畴，则归类为无关回答，不用进行解释，禁止臆测；
2.8、如果用户提到了具体的打分，请提取出相关分数；如果没有，则无需提出；

输出之前先检查"不满意原因-分类"结果是否严格遵循1.1到1.8和2.1到2.7的所有要求，最终输出无需进行解释，且格式如下：
{
 "不满意原因-分类":["xx", "xx"],
 "不满意评分":""
}', '', '默认prompt', 0, NULL, NULL, '111111', '2024-01-28 17:34:56.000', NULL, NULL);

-- v1.7.5号码接通率告警
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(407000793, '吴川', '18119609923', 238884052, 1, 0, '', 0, NULL, NULL, NULL, '2024-10-15 17:19:59.714', '1204864131342336', '2024-10-15 17:19:59.714', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(238884052, '号码接通率告警', 'caller_alarm', -1, 1, 0, '', 0, NULL, NULL, NULL, '2024-10-15 17:19:51.093', '1204864131342336', '2024-10-15 17:19:51.093', NULL);


-- v1.7.8自由调度
-- 自由调度开启的话术配置
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(633203429, '1844254897676289', 'llmFreeSelect', -1, 1, 0, '大模型自由调度话术', 0, NULL, NULL, NULL, '2024-12-16 09:03:37.145', '1645668729749504', '2024-12-16 09:03:37.145', '1645668729749504');
-- 知识库节点说明
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(409553539, '知识库节点说明', 'knowledgeNameExplain', -1, 1, 0, '', 0, NULL, NULL, NULL, '2024-12-25 09:11:21.637', '1645668729749504', '2024-12-25 09:11:21.637', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(253670786, '居民已死亡', '若用户已经死亡', 409553539, 1, 0, '', 0, NULL, NULL, NULL, '2024-12-25 09:13:40.101', '1645668729749504', '2024-12-25 09:13:40.101', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(468828560, '自动留言', '若用户电话进入自动留言阶段', 409553539, 1, 0, '', 0, NULL, NULL, NULL, '2024-12-25 09:13:33.753', '1645668729749504', '2024-12-25 09:13:33.753', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(270515462, '接听不便', '若用户表达不方便接听电话', 409553539, 1, 0, '', 0, NULL, NULL, NULL, '2024-12-25 09:13:18.781', '1645668729749504', '2024-12-25 09:13:18.781', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(861782587, '号码错误', '若用户表达号码错误', 409553539, 1, 0, '', 0, NULL, NULL, NULL, '2024-12-25 09:13:09.129', '1645668729749504', '2024-12-25 09:13:09.129', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(297731347, '不愿配合', '若用户表达接听不便', 409553539, 1, 0, '', 0, NULL, NULL, NULL, '2024-12-25 09:12:44.086', '1645668729749504', '2024-12-25 09:12:44.086', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(637672429, '没听清', '若用户表达没有听清', 409553539, 1, 0, '', 0, NULL, NULL, NULL, '2024-12-25 09:12:51.996', '1645668729749504', '2024-12-25 09:12:51.996', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(274123980, '重复拨打', '若用户表达电话已拨打过', 409553539, 1, 0, '', 0, NULL, NULL, NULL, '2024-12-25 09:13:00.379', '1645668729749504', '2024-12-25 09:13:00.379', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(253860022, '问身份', '若用户表达问身份或者询问医生是不是机器人', 409553539, 1, 0, '', 0, NULL, NULL, NULL, '2024-12-25 09:11:51.737', '1645668729749504', '2024-12-25 09:11:51.737', '1645668729749504');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(464452882, '问机器人', '若用户询问医生是不是机器人', 409553539, 1, 0, '', 0, NULL, NULL, NULL, '2024-12-25 09:13:27.273', '1645668729749504', '2024-12-25 09:13:27.273', NULL);

--节点说明模板
INSERT INTO tb_aicc_llm_prompt_template (id, name, template_content, variables, description, default_flag, tenant_id, revision, created_by, created_time, updated_by, updated_time) VALUES(***********, '节点说明生成', '这是一个医疗外呼随访场景，用户将对【医生问题】进行回答，且用户的意图会落入【节点意图分类】中。
请根据下面的【医生问题】，为【节点意图分类】中的每个节点，根据其字面意思，生成每个节点的选择判断逻辑，格式为“若xxx”
##医生问题
${playContent}
##节点意图分类
${linkNames}
输出要求：
1、控制在一两句话内，简洁、语句通顺即可
2、生成的逻辑说明不与其他节点混淆
3、不要增加或遗漏原有节点
4、不要提到【医生问题】中特定的时间点、医生医院名字等，因为这些都是后配的、允许填槽的内容，而我们生成的逻辑是通用、泛化的
5、如果医生问题中涉及对次数、时间等询问，且此时【节点意图分类】中存在“不确定”节点，此时“不确定”的选择判断逻辑可以参考“若用户无法确定具体的次数/时间，或者表达了多个次数/时间”来生成', NULL, '节点说明生成', 0, NULL, NULL, '111111', '2024-12-27 13:47:44.968', NULL, NULL);

-- 放音大模型模板调整
UPDATE tb_aicc_llm_prompt_template SET template_content='这是一个智能外呼场景，下面的"历史对话内容"是医生和患者的历史对话，"建议回复"是通过规则设定的针对用户问题的一个建议回复。
##以下是用户画像
${portraitContent}
##历史对话内容
${historyText}
##建议回复
${examplePlayText}
##任务要求
1. 以纯文本进行回复；
2. 回复整体要保持礼貌；
3. 回答字数和建议回复相差不超过20个字；
4. 直接进行回答，不需要加 “医生：”的前缀；
5. 不要询问无关内容；
6. 回复不要过于冗余，如果建议回复以问句结尾，你的回复也需要以问句结尾，不需要在问句后面再补充如“希望您一切都好”等，也不要把一个问题拆开成多个子问题，问题数量和建议回复的问题数量保持一致即可；如果建议回复以陈述句结尾，你的回复也需要以陈述句结尾；
7. 不要擅自推测性别，不要主观性的加“先生”、“女士”等后缀，尽量避免称呼患者的名字，用您/他/她等称呼即可；
8. 你可以从“历史对话内容”和“建议回复”中推理出讨论的患者是当前正在通话中的人，还是在讨论其他人，从而区分您和他/她，比如：如果在讨论的是他的饮食/自理情况/症状等，就需要用他的饮食/自理情况/症状等，而不是您的饮食/自理情况/症状等；
9. 不要变动建议回复的一些关键词，比如：不要把“上升”、“下降”换成“变化”，不要把“能否自理”换成“能否照顾好自己”等，直接使用建议回复中的词即可；
10. 如果需要，可以在你给出的回复前面，对患者的情况和问题进行一定的回应，让整体的回复更个性化。比如：患者患病或者身心不舒服的时候，给出一定的安慰共情；患者身心健康或者症状痊愈的时候，给出一定的积极回应。但是要确保这些回应加上之后，不会改变回复的主体意思，整体语义和内容尽量不变。' WHERE id=***********;

-- v1.7.9
-- 异常标签字典配置
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (217198576, '异常类型', 'abnormal_type', -1, 1, 0, '异常类型', 0, NULL, NULL, NULL, '2024-08-17 15:05:55.36633', '1645668729749504', '2024-08-17 15:05:55.36633', '1645668729749504');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (232596545, '异常原因分类', 'abnormal_reason', -1, 1, 0, '一般、较重、严重', 0, NULL, NULL, NULL, '2024-08-17 15:13:58.337512', '1645668729749504', '2024-08-17 15:13:58.337512', '1645668729749504');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (407846861, '人工干预', 'artificial_intervention', -1, 1, 0, '人工干预', 0, NULL, NULL, NULL, '2024-08-17 15:19:27.975488', '1645668729749504', '2024-08-17 15:19:27.975488', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (270939936, 'AI预处理', 'ai_preprocessing', -1, 1, 0, '健康宣教、建议复诊、尽快就医、AI继续跟', 0, NULL, NULL, NULL, '2024-08-17 15:21:31.054989', '1645668729749504', '2024-08-17 15:21:31.054989', NULL);


INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (610625569, '随访病情异常', 'follow', 217198576, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:25:12.201126', '1645668729749504', '2024-08-17 15:25:12.201126', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (466319672, '睡眠情况异常', 'sleep', 217198576, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:30:04.336137', '1645668729749504', '2024-08-17 15:30:04.336137', '1645668729749504');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (696103070, '饮食情况异常', 'diet', 217198576, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:29:23.998893', '1645668729749504', '2024-08-17 15:29:23.998893', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (884322440, '复诊依从性异常', 'referral', 217198576, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:28:07.219396', '1645668729749504', '2024-08-17 15:28:07.219396', '1645668729749504');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (443568938, '用药依从性异常', 'medication', 217198576, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:26:43.859546', '1645668729749504', '2024-08-17 15:26:43.859546', '1645668729749504');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (405901140, '满意度询问', 'satisfaction', 217198576, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:32:20.506893', '1645668729749504', '2024-08-17 15:32:20.506893', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (274906168, '较重', 'heavier', 232596545, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:34:46.767785', '1645668729749504', '2024-08-17 15:34:46.767785', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (489779938, '一般', 'normal', 232596545, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:35:37.545541', '1645668729749504', '2024-08-17 15:35:37.545541', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (637237913, '严重', 'severe', 232596545, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:33:37.256246', '1645668729749504', '2024-08-17 15:33:37.256246', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (696331723, 'AI继续跟进', 'ai_follow_up', 270939936, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:43:54.989573', '1645668729749504', '2024-08-17 15:43:54.989573', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (468212470, '健康宣教', 'health_education', 270939936, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:36:59.138787', '1645668729749504', '2024-08-17 15:36:59.138787', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (825893580, '尽快就医', 'medical_treatment', 270939936, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:43:02.725913', '1645668729749504', '2024-08-17 15:43:02.725913', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES (692976014, '建议复诊', 'suggestion_return_visit', 270939936, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-17 15:42:14.647994', '1645668729749504', '2024-08-17 15:42:14.647994', NULL);

-- 异常标签表
DROP TABLE IF EXISTS tb_aicc_speech_abnormal_label;
CREATE TABLE tb_aicc_speech_abnormal_label(
    id int8 NOT NULL,
    rel_id int8,
    rel_type int2,
    logical_operation varchar(16),
    label_name varchar(100),
    creator varchar(64),
    create_time timestamp,
    sort int8,
    abnormal_type varchar(100) DEFAULT  '',
    abnormal_reason varchar(100) DEFAULT  '',
    artificial_intervention varchar(100) DEFAULT  '',
    ai_preprocessing varchar(100) DEFAULT  '',
    PRIMARY KEY (id)
);
CREATE INDEX tb_aicc_speech_abnormal_label_speech_id_idx ON tb_aicc_speech_abnormal_label(rel_type,rel_id);

COMMENT ON TABLE tb_aicc_speech_abnormal_label IS '话术异常标签表';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.id IS '主键id';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.rel_id IS '关联话术/片段id';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.rel_type IS '关联话术类型 1-片段 2-话术';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.logical_operation IS '逻辑操作运算符';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.label_name IS '异常标签名';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.creator IS '创建者';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.create_time IS '创建时间';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.abnormal_type IS '异常类型';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.abnormal_reason IS '异常原因分类';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.artificial_intervention IS '人工干预';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label.ai_preprocessing IS 'AI预处理';


DROP TABLE IF EXISTS tb_aicc_speech_abnormal_label_item;
CREATE TABLE tb_aicc_speech_abnormal_label_item(
    id int8 NOT NULL,
    label_id int8,
    interface_id int8,
    field_id int8,
    field varchar(100),
    compare_code varchar(16),
    compare_content_name varchar(100),
    compare_content varchar(100),
    create_time timestamp,
    creator varchar(64),
    sort int8,
    PRIMARY KEY (id)
);
CREATE INDEX tb_aicc_speech_abnormal_label_item_label_id_idx ON tb_aicc_speech_abnormal_label_item(label_id);

COMMENT ON TABLE tb_aicc_speech_abnormal_label_item IS '异常标签条目表';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label_item.id IS '主键';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label_item.label_id IS '标签id';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label_item.interface_id IS '接口id';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label_item.field_id IS '字段名';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label_item.field IS '字段名';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label_item.compare_code IS '比较符';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label_item.compare_content_name IS '比较文本展示';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label_item.compare_content IS '比较文本或编码';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label_item.create_time IS '创建时间';
COMMENT ON COLUMN tb_aicc_speech_abnormal_label_item.creator IS '创建者';


ALTER TABLE tb_aicc_llm_prompt_template ADD template_type int2 DEFAULT 0 NULL;
COMMENT ON COLUMN tb_aicc_llm_prompt_template.template_type IS '模板类型 0-未知 1-放音内容改写模型 2-通用（专病）模型 3-满意度模型 4-生成节点说明模型';


update tb_aicc_llm_prompt_template
set name = '放音内容改写模型', template_type = 1,created_by = '100001'
where id = ***********;

update tb_aicc_llm_prompt_template
set name = '通用（专病）模型', template_type = 2 ,created_by = '100001'
where id = ***********;

update tb_aicc_llm_prompt_template
set name = '满意度模型', template_type = 3 ,created_by = '100001'
where id = ***********;

update tb_aicc_llm_prompt_template
set name = '生成节点说明模型', template_type = 4 ,created_by = '100001'
where id = ***********;


INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(652846324, '大模型模板类型', 'llmTemplateType', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-16 18:32:17.622', '1645668729749504', '2025-01-16 18:32:17.622', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(278592188, '放音内容改写模型', '1', 652846324, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-16 18:38:48.975', '1645668729749504', '2025-01-16 18:38:48.975', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(679053655, '通用（专病）模型', '2', 652846324, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-16 18:39:01.298', '1645668729749504', '2025-01-16 18:39:01.298', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(405903277, '满意度模型', '3', 652846324, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-16 18:39:16.116', '1645668729749504', '2025-01-16 18:39:16.116', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(466355141, '生成节点说明模型', '4', 652846324, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-16 18:39:24.324', '1645668729749504', '2025-01-16 18:39:24.324', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(631724608, '节点生成说明', 'generateNodeExplain', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-17 09:55:30.220', '1645668729749504', '2025-01-17 09:55:30.220', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(658904461, 'MT3', 'MT3', 631724608, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-17 09:55:38.651', '1645668729749504', '2025-01-17 09:55:38.651', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(215428582, '放音内容改写', 'playContent', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-17 09:54:42.889', '1645668729749504', '2025-01-17 09:54:42.889', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(276239743, 'WH2', 'WH2', 215428582, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-17 09:57:21.930', '1645668729749504', '2025-01-17 09:57:21.930', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(633203400, '节点选择', 'nodeSelect', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-17 09:25:10.894', '1645668729749504', '2025-01-17 09:25:10.894', '1645668729749504');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(848492161, 'WH1', 'WH1', 633203400, 1, 0, '', 0, NULL, NULL, NULL, '2025-01-17 09:57:09.357', '1645668729749504', '2025-01-17 09:57:09.357', NULL);

-- v1.8.0
ALTER TABLE tb_aicc_speech_node_result ADD llm_result varchar(1024) NULL;
COMMENT ON COLUMN tb_aicc_speech_node_result.llm_result IS '大模型结果;json文本';


--v1.8.1
ALTER TABLE tb_aicc_speech_base ADD temp_flow_file_path varchar(500) NULL;
COMMENT ON COLUMN tb_aicc_speech_base.temp_flow_file_path IS '话术制作过程中间临时文件地址';
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(460063479, '大模型离线解析版本', 'llmAnalysis', -1, 1, 0, '大模型离线解析版本', 0, NULL, NULL, NULL, '2025-03-17 14:19:12.198', '1645668729749504', '2025-03-17 14:19:12.198', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(215067901, 'MT3', 'MT3', 460063479, 1, 0, '', 0, NULL, NULL, NULL, '2025-03-17 14:19:39.398', '1645668729749504', '2025-03-17 14:19:39.398', NULL);

INSERT INTO tb_aicc_llm_prompt_template (id, "name", template_content, variables, description, default_flag, tenant_id, revision, created_by, created_time, updated_by, updated_time, template_type) VALUES(10000000005, '节点选择指令（自由调度）', '下面是一个医院打电话给用户进行随访的场景，下面的"医生问题"是医生提出的问题，"用户回答"是用户给出的回答。
##医生问题
医生：${currentQuestion}
##用户回答
用户：${currentAnswer}
对于用户的回答，预先定义了如下的“选择逻辑和话术节点”供医生选择，作为医生下一个要说的话术节点。
##选择逻辑和话术节点
${nextQuestion}若用户的说法不在上述话术的范围中，则话术节点为：拒识
请你根据“选择逻辑和话术节点”中提供的选择逻辑，选择一个或者多个最合适的话术节点。只输出话术节点的名称即可。', NULL, NULL, 0, NULL, NULL, '1204864131342336', '2025-03-21 16:52:52.438', NULL, NULL, 2);

ALTER TABLE aicc_flow.tb_aicc_speech_node_result ADD rewrite_first_response_time int8 NULL;
COMMENT ON COLUMN aicc_flow.tb_aicc_speech_node_result.rewrite_first_response_time IS '答案生成大模型首响耗时';
ALTER TABLE aicc_flow.tb_aicc_speech_node_result ADD rewrite_total_response_time int8 NULL;
COMMENT ON COLUMN aicc_flow.tb_aicc_speech_node_result.rewrite_total_response_time IS '答案生成大模型总耗时';
ALTER TABLE aicc_flow.tb_aicc_speech_node_result ADD node_select_first_response_time int8 NULL;
COMMENT ON COLUMN aicc_flow.tb_aicc_speech_node_result.node_select_first_response_time IS '节点选择大模型首响耗时';
ALTER TABLE aicc_flow.tb_aicc_speech_node_result ADD node_select_total_response_time int8 NULL;
COMMENT ON COLUMN aicc_flow.tb_aicc_speech_node_result.node_select_total_response_time IS '节点选择大模型总耗时';

-- 历史迭代遗漏脚本
INSERT INTO tb_aicc_speech_knowledge (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, "name", description, "condition", "type", deleted, status, process_type, prompt_content, knowledge_type, speech_id) VALUES(NULL, NULL, '1563035689754624', '2023-11-03 16:14:05.494', '1279578451247104', '2025-01-14 10:13:55.187', 1537595205885952, '问身份', '', '问身份', 2, 0, 1, 1, '[{"text":"我是#{机构名称}的工作人员，","audioList":{},"interrupt":false,"type":1,"variableMapping":{"机构名称":"单位名称,外呼机构"}}]', 0, NULL);
INSERT INTO tb_aicc_speech_knowledge (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, "name", description, "condition", "type", deleted, status, process_type, prompt_content, knowledge_type, speech_id) VALUES(NULL, NULL, '1563035689754624', '2023-11-03 16:23:29.393', '1279578451247104', '2024-09-19 18:35:58.630', 1537663690481664, '重复拨打', '', '重复拨打', 2, 0, 1, 2, '[{"text":"抱歉，打扰您了，再见！","audioList":{},"interrupt":false,"type":1,"variableMapping":{}}]', 0, NULL);
INSERT INTO tb_aicc_speech_knowledge (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, "name", description, "condition", "type", deleted, status, process_type, prompt_content, knowledge_type, speech_id) VALUES(NULL, NULL, '1563035689754624', '2023-10-23 13:44:23.284', NULL, '2024-09-19 18:36:04.603', 1537664252518400, '自动留言', '', '自动留言', 2, 0, 1, 2, '[{"text":"好的，再见！","audioList":{},"interrupt":false,"type":1,"variableMapping":{}}]', 0, NULL);
INSERT INTO tb_aicc_speech_knowledge (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, "name", description, "condition", "type", deleted, status, process_type, prompt_content, knowledge_type, speech_id) VALUES(NULL, NULL, '1560394595221504', '2023-10-23 11:29:44.373', '100001', '2024-09-10 10:08:43.043', 1537596480954368, '不愿配合', '', '不愿配合', 2, 0, 1, 2, '[{"text":"抱歉，打扰您了，再见！","audioList":{},"interrupt":false,"type":1,"variableMapping":{}}]', 0, NULL);
INSERT INTO tb_aicc_speech_knowledge (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, "name", description, "condition", "type", deleted, status, process_type, prompt_content, knowledge_type, speech_id) VALUES(NULL, NULL, '1560394595221504', '2023-11-03 16:25:27.688', NULL, '2024-09-10 10:08:47.247', 1542625661190144, '号码错误', '', '号码错误', 2, 0, 1, 2, '[{"text":"抱歉，打扰您了，再见！","audioList":{"天地玄黄，宇宙洪荒。 日月盈昃，辰宿列张。 寒来暑往，秋收冬藏。 闰余成岁，律吕调阳。":"/outbound-plat-bucket/outbound/test/file/2023-11-03/5cf821e5cfaa43e8bfb5958c92a95040.wav"},"interrupt":false,"type":1,"variableMapping":{}}]', 0, NULL);
INSERT INTO tb_aicc_speech_knowledge (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, "name", description, "condition", "type", deleted, status, process_type, prompt_content, knowledge_type, speech_id) VALUES(NULL, NULL, '1560394595221504', '2023-11-03 15:35:04.172', '1279578451247104', '2024-09-10 10:08:53.143', 1537582706851840, '接听不便', '', '接听不便', 2, 0, 1, 2, '[{"text":"抱歉，打扰您了，再见！","audioList":{},"interrupt":false,"type":1,"variableMapping":{}}]', 0, NULL);
INSERT INTO tb_aicc_speech_knowledge (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, "name", description, "condition", "type", deleted, status, process_type, prompt_content, knowledge_type, speech_id) VALUES(NULL, NULL, '1560394595221504', '2023-10-30 10:03:29.527', '100001', '2024-09-10 10:09:03.656', 1542626500050944, '居民已死亡', '', '居民已死亡', 2, 0, 1, 2, '[{"text":"抱歉，打扰您了，再见！","audioList":{},"interrupt":false,"type":1,"variableMapping":{}}]', 0, NULL);
INSERT INTO tb_aicc_speech_knowledge (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, "name", description, "condition", "type", deleted, status, process_type, prompt_content, knowledge_type, speech_id) VALUES(NULL, NULL, '100001', '2024-10-15 17:32:40.735', NULL, '2024-11-28 21:14:31.941', 1797248863363078, '没听清', '', '没听清', 2, 0, 1, 1, NULL, 0, NULL);
INSERT INTO tb_aicc_speech_knowledge (tenant_id, revision, created_by, created_time, updated_by, updated_time, id, "name", description, "condition", "type", deleted, status, process_type, prompt_content, knowledge_type, speech_id) VALUES(NULL, NULL, '1563035689754624', '2023-11-03 16:12:19.936', '100001', '2025-01-14 10:13:48.824', 1537595835031552, '问机器人', '', '问机器人', 2, 0, 1, 1, '[{"text":"我是#{机构名称}的随访助理，","audioList":{},"interrupt":false,"type":1,"variableMapping":{"机构名称":"外呼机构,自我介绍,单位名称"}}]', 0, NULL);

INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(698443171, '通用结束节点', 'common_end_node', -1, 1, 0, '通用结束节点配置', 0, NULL, NULL, NULL, '2024-07-25 11:19:31.033', '1645668729749504', '2024-07-25 11:19:31.033', '1645668729749504');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(299298389, '号码错误', '好的，那不打扰您了，感谢您的接听，祝您生活愉快，再见。', 698443171, 1, 0, '', 0, NULL, NULL, NULL, '2024-08-08 21:21:23.512', '100001', '2024-08-08 21:21:23.512', '1563036184682496');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(692934631, '接听不便', '好的，那不打扰您了，感谢您的接听，再见。', 698443171, 1, 1, '', 0, NULL, NULL, NULL, '2024-09-14 15:12:39.012', '1563035689754624', '2024-09-14 15:12:39.012', '1563036184682496');

-- v1.8.2
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(217745850, 'TIYAN', 'TIYAN', 259964165, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-08 22:13:45.558', '1204864131342336', '2025-04-08 22:13:45.558', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(259964165, 'domain强制走工程院模型', 'medicalDomain', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-08 22:13:36.617', '1204864131342336', '2025-04-08 22:13:36.617', '1204864131342336');

INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(211624228, 'speechId', '1903137389322240,1903737216737280,1903884503916544,1907950336385024,1908103755636736,1908171225210880,1908673199513600,1909656243707904,1910934919225344,1910980687470592', 259584303, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-08 15:55:00.160', '1204864131342336', '2025-04-08 15:55:00.160', '1204864131342336');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(259584303, '精细化话术id', 'refinedSpeechId', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-08 15:54:31.358', '1204864131342336', '2025-04-08 15:54:31.358', '1204864131342336');

INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(696719711, 'TIYAN', 'medical-outbound-tiyan#wss://cbm-cn-huabei-1.xf-yun.com/', 270137756, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-08 14:29:34.237', '1204864131342336', '2025-04-08 14:29:34.237', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(671052557, 'OUTBOUND', 'medical-outbound-prod#wss://cbm-cn-huabei-1.xf-yun.com/', 270137756, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-08 14:29:19.607', '1204864131342336', '2025-04-08 14:29:19.607', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(230613458, 'default', 'medical-m2#wss://cbm-cn-huabei-1.xf-yun.com/', 270137756, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-08 14:28:04.692', '1204864131342336', '2025-04-08 14:28:04.692', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(270137756, '工程院模型地址', 'medicalModelUrl', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-08 14:26:33.405', '1204864131342336', '2025-04-08 14:26:33.405', NULL);

INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(633015689, '1-星火平台，2-gpt，3-图聆，4-豆包，5-工程院', '1', 219665431, 1, 0, '', 0, NULL, NULL, NULL, '2025-03-31 19:22:02.268', '100001', '2025-03-31 19:22:02.268', '1204864131342336');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(219665431, '大模型调用方式', 'llmServiceType', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-03-31 19:21:35.361', '100001', '2025-03-31 19:21:35.361', NULL);

update tb_aicc_llm_prompt_template set template_content = '下面是一个医院打电话给用户进行随访的场景，下面的"医生问题"是医生提出的问题，"用户回答"是用户给出的回答。
##医生问题
医生：${currentQuestion}
##用户回答
用户：${currentAnswer}
对于用户的回答，预先定义了如下的“选择逻辑和话术节点”供医生选择，作为医生下一个要说的话术节点。
##选择逻辑和话术节点
${nextQuestion}
请你根据“选择逻辑和话术节点”中提供的选择逻辑，选择一个最合适的话术节点。只输出话术节点的序号即可。' where id = ***********;
update tb_aicc_llm_prompt_template set template_content = '下面是一个医院打电话给用户进行随访的场景，下面的"医生问题"是医生提出的问题，"用户回答"是用户给出的回答。
##医生问题
医生：${currentQuestion}
##用户回答
用户：${currentAnswer}
对于用户的回答，预先定义了如下的“选择逻辑和话术节点”供医生选择，作为医生下一个要说的话术节点。
##选择逻辑和话术节点
${nextQuestion}
请你根据“选择逻辑和话术节点”中提供的选择逻辑，选择一个或者多个最合适的话术节点。只输出话术节点的序号即可。' where id = 10000000005;
INSERT INTO tb_aicc_llm_prompt_template (id, "name", template_content, variables, description, default_flag, tenant_id, revision, created_by, created_time, updated_by, updated_time, template_type) VALUES(10000000007, '2.6B模型判断单/多意图模板', '下面是一个意图分类任务，需要根据随访对话，判断用户的意图是单个还是多个。
##医生问题
医生：${currentQuestion}
##用户回答
用户：${currentAnswer}
##要求：理解医生问题和用户回答，并进行下面的判断；
1、若用户回答包含的意图只有1个，则输出"单"；
2、若用户回答包含的意图有2个及以上，则输出"多"；
输出：单/多', NULL, NULL, 0, NULL, NULL, '1204864131342336', '2025-04-23 19:16:39.834', '1204864131342336', '2025-04-27 14:07:09.258', 2);

-- 高置信度开关字典
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(407021873, '开关', 'true', 869344176, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-27 19:08:27.841', '1279578451247104', '2025-04-27 19:08:27.841', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(869344176, '高置信度开关-打开后调用小模型和语义，若置信度高则返回语义结果', 'highConfidenceSwitch', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-27 19:08:15.286', '1279578451247104', '2025-04-27 19:08:15.286', NULL);
-- 多意图模型默认domain
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(671649821, 'medical', 'medical', 299440906, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-27 19:02:54.232', '1279578451247104', '2025-04-27 19:02:54.232', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(299440906, '判断是否多意图', 'multiIntention', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-27 19:02:48.384', '1279578451247104', '2025-04-27 19:02:48.384', NULL);
-- 工程院新增小模型地址字典
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(612731276, 'medical', 'medical#ws://172.30.94.38:8090/v1/private/medicaldev5', 270137756, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-27 19:03:19.764', '1279578451247104', '2025-04-27 19:03:19.764', NULL);
-- 小模型目前强制走工程院
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(865960520, 'medical', 'medical', 259964165, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-27 19:03:06.607', '1279578451247104', '2025-04-27 19:03:06.607', NULL);
INSERT INTO tb_aicc_llm_prompt_template (id, "name", template_content, variables, description, default_flag, tenant_id, revision, created_by, created_time, updated_by, updated_time, template_type) VALUES(10000000006, '放音内容改写模型（精细化）', '这是一个智能外呼场景，下面的“用户画像”是用户的就医档案信息，"历史对话上文"是医生和患者的历史对话，“当前轮的对话”是当前最新轮次的医患对话，"建议回复"是预定义的回复内容。请你结合这几部分内容，重点结合当前轮的对话，生成最终的回复内容。

## 以下是用户画像
${portraitContent}

## 历史对话上文
${historyText}

## 当前轮的对话
${currentText}

## 建议回复
${examplePlayText}

## 任务要求
1. 请你参考建议回复，用纯文本进行回复，回复整体要保持礼貌并不要询问无关内容；
2. 回复不要过于冗余，如果建议回复以问句结尾，你的回复也需要以问句结尾（也不要把一个问题拆开成多个子问题，问题数量和建议回复的问题数量保持一致即可）。如果建议回复以陈述句结尾，你的回复也需要以陈述句结尾；
3. 不要擅自推测性别，不要主观性地加“先生”、“女士”等后缀，尽量避免称呼患者的名字，用您/他/她等称呼即可；
4. 不要变动建议回复的一些关键词，比如：不要把”上升“、”下降“换成”变化“，不要把”能否自理“换成”能否照顾好自己“等，直接使用建议回复中的词即可；
5. 请你在参考建议回复生成最终回复内容时：
1）判断【当前轮的对话】是否属于延续【历史对话上文】的追问，如果不是，此时仅结合当前轮的对话即可，不要对更早的患者表述进行解读或回应；反之则需要结合相关的【历史对话上文】
2）对于以下场景，你需要首先输出分析内容，然后将建议回复中提问的问题进行输出。
  - 用户回复有关自身症状（包括一些异常情况如不能走路、无法自理等）时：
	  - 当用户描述了具体症状时，你需要针对症状进行分析（症状可能的诱因等，多个症状关联不大时可以分别进行分析），然后给出指导建议，最后风险兜底
	  - 当用户描述了症状的状态（如是否新发、好转情况、持续时间、严重程度等）时，你需要针对该状态进行解读，然后给出指导建议，最后风险兜底
  - 用户描述有关自身疾病时，你需要针对疾病进行指导建议，然后给出风险兜底
  - 用户回复有关自身异常指标（具体数值或者偏高，偏低的趋势），你需要进行指标分析（该异常指标的趋势如偏高/偏低，提示什么情况/有什么风险），然后给出指导建议，最后风险兜底
  - 用户回复有关药物副作用时，你需要结合药物和副作用症状进行分析，然后给出指导建议，最后风险兜底
  - 上述分析内容每部分1-2句话即可，总字数80字以内
  - 若用户表述自身无症状、无疾病、指标无异常、无药物副作用时，则不需要进行分析解读
  - 若用户表述不知道、不清楚、不确定等时，也不需要进行分析解读
3）在用户提出反问时，你需要对该内容进行解释，然后将建议回复中提问的问题进行输出。
4）当用户对于一些依从性类的问题，如是否按时服药、是否坚持运动等，给出了否定的回复时，你需要给出一些未依从的危害、以及指导建议，然后将建议回复中提问的问题进行输出。
5）在其他场景下，你需要参考建议回复生成最终回复内容即可（必要时需要给用户提供宣教共情内容）。
', NULL, NULL, 0, NULL, NULL, '1204864131342336', '2025-04-08 20:42:29.542', '1204864131342336', '2025-04-08 20:49:57.380', 1);

INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(238042398, '极速版聆玉言', 'x5_lingyuyan_flow', 612560527, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-23 20:44:26.597', '1204864131342336', '2025-04-23 20:44:26.597', '1204864131342336');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(612560527, '工程院发音人不攒句', 'medicalVoiceCollect', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-23 20:43:56.994', '1204864131342336', '2025-04-23 20:43:56.994', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(614467656, '引擎阈值', 'thresh', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-05-07 14:02:16.163', '1659489716338688', '2025-05-07 14:02:16.163', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(272648378, 'bertThresh', '0.85', 614467656, 1, 0, 'bert模型得分阈值门限', 0, NULL, NULL, NULL, '2025-05-07 14:04:09.705', '1659489716338688', '2025-05-07 14:04:09.705', '1659489716338688');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(291865457, 'modeThresh', '0.85', 614467656, 1, 0, 'lstm模型阈值门限', 0, NULL, NULL, NULL, '2025-05-07 14:02:55.716', '1659489716338688', '2025-05-07 14:02:55.716', '1659489716338688');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(481960362, 'strongThresh', '0.9', 614467656, 1, 0, '强规则得分阈值门限', 0, NULL, NULL, NULL, '2025-05-07 14:03:38.337', '1659489716338688', '2025-05-07 14:03:38.337', NULL);
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(806007354, 'config', '{     "asrServiceName": "iat",     "ttsServiceName": "stts",     "silentDetectionTime": "750",     "caller": "055162392904",     "vid": "x4_lingyuyan_oral",     "speed": "55" }', 806636584, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-18 20:49:49.864', '1204864131342336', '2025-04-18 20:49:49.864', '100001');
INSERT INTO tb_aicc_system_dict (id, dict_name, dict_value, pid, dict_flag, rank_id, remark, deleted, general_field1, general_field2, general_field3, create_time, creator, update_time, updater) VALUES(806636584, '智能体配置', 'agentConfig', -1, 1, 0, '', 0, NULL, NULL, NULL, '2025-04-18 20:47:41.363', '1204864131342336', '2025-04-18 20:47:41.363', NULL);

-- v1.8.3
ALTER TABLE tb_aicc_voice_config ADD platform_code varchar(32) NULL DEFAULT 0;
COMMENT ON COLUMN tb_aicc_voice_config.platform_code IS '业务平台编码';
ALTER TABLE tb_aicc_voice_config ADD platform_name varchar(255) NULL;
COMMENT ON COLUMN tb_aicc_voice_config.platform_name IS '业务平台名称';
ALTER TABLE tb_aicc_voice_config ADD org_id varchar(64) NULL;
COMMENT ON COLUMN tb_aicc_voice_config.org_id IS '单位ID';
ALTER TABLE tb_aicc_voice_config ADD org_name varchar(100) NULL;
COMMENT ON COLUMN tb_aicc_voice_config.org_name IS '单位名称';
ALTER TABLE tb_aicc_voice_config ADD fork_voice_source int2 NULL;
COMMENT ON COLUMN tb_aicc_voice_config.fork_voice_source IS '复刻来源 1-产品平台 2-体验工具';
ALTER TABLE tb_aicc_voice_config ADD creator_name varchar(100) NULL;
COMMENT ON COLUMN tb_aicc_voice_config.creator_name IS '创建人姓名';
--患者画像变量表
DROP TABLE IF EXISTS tb_aicc_patient_portrait_variable;
CREATE TABLE tb_aicc_patient_portrait_variable(
    id int8 NOT NULL,
    config_id int8 NOT NULL,
    name varchar(50) NOT NULL,
    code VARCHAR(255) NOT NULL,
    required int2 NOT NULL,
    limit_rule text,
    internal int2 NOT NULL,
    default_value VARCHAR(255),
    remark varchar(200),
    operator varchar(64) NOT NULL,
    created_time timestamp NOT NULL DEFAULT  CURRENT_TIMESTAMP,
    updater varchar(64),
    updated_time timestamp,
    deleted int2 NOT NULL DEFAULT  0,
    PRIMARY KEY (id)
);

COMMENT ON TABLE tb_aicc_patient_portrait_variable IS '患者画像变量表';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.id IS '主键';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.config_id IS '所属画像类别id';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.name IS '变量名称描述';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.code IS '变量编码';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.required IS '是否必填;只验证value_type=1;的场景';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.limit_rule IS '变量限制;json;长度最大、最小值';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.internal IS '变量类型;0:非内部变量 1:内部变量';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.default_value IS '变量默认值';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.remark IS '备注';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.operator IS '创建人';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.created_time IS '创建时间';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.updater IS '更新人';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.updated_time IS '更新时间';
COMMENT ON COLUMN tb_aicc_patient_portrait_variable.deleted IS '是否删除;0:正常; 1:已删除';
CREATE INDEX idx_config_delete ON tb_aicc_patient_portrait_variable(config_id,deleted);

-- v1.8.4
ALTER TABLE tb_aicc_speech_base ADD speech_mode int2 NOT NULL DEFAULT 1;
COMMENT ON COLUMN tb_aicc_speech_base.speech_mode IS '话术模式1-传统，2-大模型';
ALTER TABLE tb_aicc_speech_base ADD model_stage int2 NULL;
COMMENT ON COLUMN tb_aicc_speech_base.model_stage IS '模型阶段，1-阶段一，2-自由调度';

-- v1.8.5
-- 私有化关联用户配置
DROP TABLE IF EXISTS tb_aicc_plat_code_user;
CREATE TABLE tb_aicc_plat_code_user(
    id int8 NOT NULL,
    plat_code VARCHAR(500) NOT NULL,
    user_id VARCHAR(100) NOT NULL,
    create_time timestamp(6) DEFAULT  CURRENT_TIMESTAMP,
    creator VARCHAR(32),
	update_time timestamp(6) DEFAULT NULL,
    updater VARCHAR(32),
    deleted_time TIMESTAMP(6),
    deleted INT4  NULL DEFAULT  0,
    PRIMARY KEY (id)
);

COMMENT ON TABLE tb_aicc_plat_code_user IS '交付用户配置表';
COMMENT ON COLUMN tb_aicc_plat_code_user.id IS '主键id;主键';
COMMENT ON COLUMN tb_aicc_plat_code_user.plat_code IS '私有化平台编码;私有化平台编码';
COMMENT ON COLUMN tb_aicc_plat_code_user.user_id IS '用户id;用户id';
COMMENT ON COLUMN tb_aicc_plat_code_user.create_time IS '创建时间';
COMMENT ON COLUMN tb_aicc_plat_code_user.creator IS '操作者';
COMMENT ON COLUMN tb_aicc_plat_code_user.update_time IS '修改时间';
COMMENT ON COLUMN tb_aicc_plat_code_user.updater IS '操作者';
COMMENT ON COLUMN tb_aicc_plat_code_user.deleted_time IS '删除时间';
COMMENT ON COLUMN tb_aicc_plat_code_user.deleted IS '是否删除';

CREATE INDEX idx_plat_code_user ON tb_aicc_plat_code_user(plat_code,user_id);

-- 话术场景配置表
DROP TABLE IF EXISTS tb_aicc_speech_scene_config;
CREATE TABLE tb_aicc_speech_scene_config(
    id int8 NOT NULL,
    type int2 NOT NULL,
    name VARCHAR(255) NOT NULL,
    value VARCHAR(255) NOT NULL,
    parent_id int8 NOT NULL,
    remark varchar(500) default '',
    create_time timestamp(6) NOT NULL ,
    creator VARCHAR(32),
    update_time timestamp(6) NULL,
    updater VARCHAR(32),
    operation_id int8,
    operation_name VARCHAR(255),
	operation_value VARCHAR(255),
    operation_type int2,
    operation_parent_id int8,
    deleted int2 NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

COMMENT ON TABLE tb_aicc_speech_scene_config IS '话术场景配置表';
COMMENT ON COLUMN tb_aicc_speech_scene_config.id IS '话术配置表主键';
COMMENT ON COLUMN tb_aicc_speech_scene_config.type IS '配置类型 1-话术场景';
COMMENT ON COLUMN tb_aicc_speech_scene_config.name IS '配置名称';
COMMENT ON COLUMN tb_aicc_speech_scene_config.value IS '配置值';
COMMENT ON COLUMN tb_aicc_speech_scene_config.parent_id IS '父级ID';
COMMENT ON COLUMN tb_aicc_speech_scene_config.remark IS '备注';
COMMENT ON COLUMN tb_aicc_speech_scene_config.create_time IS '创建时间';
COMMENT ON COLUMN tb_aicc_speech_scene_config.creator IS '创建者';
COMMENT ON COLUMN tb_aicc_speech_scene_config.update_time IS '更新时间';
COMMENT ON COLUMN tb_aicc_speech_scene_config.updater IS '更新者';
COMMENT ON COLUMN tb_aicc_speech_scene_config.operation_id IS '对应运管主键id';
COMMENT ON COLUMN tb_aicc_speech_scene_config.operation_name IS '对应运管配置名称';
COMMENT ON COLUMN tb_aicc_speech_scene_config.operation_value IS '对应运管配置值';
COMMENT ON COLUMN tb_aicc_speech_scene_config.operation_type IS '对应运管配置类型;1-应用场景  3-话术标签 4-业务方向';
COMMENT ON COLUMN tb_aicc_speech_scene_config.operation_parent_id IS '对应运管父级id';
COMMENT ON COLUMN tb_aicc_speech_scene_config.deleted IS '删除标记';

-- 增加节点类型
ALTER TABLE tb_aicc_speech_node_result ADD node_type int2 NULL;
COMMENT ON COLUMN tb_aicc_speech_node_result.node_type IS '节点类型';